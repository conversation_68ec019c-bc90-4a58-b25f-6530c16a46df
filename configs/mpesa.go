package configs

import (
	"os"

	"github.com/itunza/telegram-silcore/internal/types"
)

func LoadMPesaConfig() types.MPesaConfig {
	return types.MPesaConfig{
		ConsumerKey:    getEnv("MPESA_CONSUMER_KEY", ""),
		ConsumerSecret: getEnv("MPESA_CONSUMER_SECRET", ""),
		PassKey:        getEnv("MPESA_PASS_KEY", ""),
		BusinessCode:   getEnv("MPESA_BUSINESS_CODE", ""),
		CallbackURL:    getEnv("MPESA_CALLBACK_URL", ""),
		Environment:    getEnv("MPESA_ENVIRONMENT", "sandbox"),
	}
}

func getEnv(key, defaultValue string) string {
	if value, exists := os.LookupEnv(key); exists {
		return value
	}
	return defaultValue
}
