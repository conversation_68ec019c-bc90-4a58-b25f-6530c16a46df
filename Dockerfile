# Build stage
FROM golang:1.22-alpine AS builder
WORKDIR /app
COPY . .
RUN go mod download
RUN CGO_ENABLED=0 GOOS=linux go build -o /bot ./cmd/bot

# Migration stage
FROM golang:1.22-alpine AS migrator
WORKDIR /app
COPY . .
RUN go mod download

# Runtime stage
FROM alpine:3.18
WORKDIR /
COPY --from=builder /bot /bot
COPY --from=builder /app/scripts/migrations /scripts/migrations
COPY .env .env
EXPOSE 8080
CMD ["/bot"]
