# Database Schema Documentation

## Redis Cache Structure (Active Usage)
### Product Cache
- Key format: `product:<productID>`
- Data structure:
  ```json
  {
    "version": "v1",
    "data": {
      "ID": "string",
      "CategoryID": "string",
      "Name": "string",
      "Price": number,
      "StockQty": integer
    }
  }
  ```

### Category Products Cache  
- Key format: `category:<categoryID>:products`
- Data structure:
  ```json
  {
    "version": "v1",
    "data": [
      // Array of Product objects
    ]
  }
  ```

### Session Storage
- Key format: `session:<userID>`
- Stores:
  ```json
  {
    "user_id": "number",
    "cart": {
      "<productID>": "quantity"
    },
    "conversation_state": "string"
  }
  ```

## PostgreSQL Schema (Planned Implementation)
```sql
-- Users Table (Future)
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  telegram_id BIGINT UNIQUE NOT NULL,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Products Table (Mirror of Redis cache)
CREATE TABLE products (
  id VARCHAR(255) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  category_id VARCHAR(255) NOT NULL,
  price DECIMAL(10,2) NOT NULL,
  last_stock_check INT NOT NULL,
  last_updated TIMESTAMP DEFAULT NOW()
);

-- Orders Table (Future)
CREATE TABLE orders (
  id SERIAL PRIMARY KEY,
  user_id INT REFERENCES users(id),
  total DECIMAL(10,2) NOT NULL,
  status VARCHAR(50) NOT NULL,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Order Items Table (Future)  
CREATE TABLE order_items (
  order_id INT REFERENCES orders(id),
  product_id VARCHAR(255) REFERENCES products(id),
  quantity INT NOT NULL,
  price DECIMAL(10,2) NOT NULL,
  PRIMARY KEY (order_id, product_id)
);
```

## ERPNext Data Mapping
```go
// Raw ERPNext API response structure
type ERPNextItem struct {
  Name          string  `json:"name"`
  ItemName      string  `json:"item_name"`
  ItemGroup     string  `json:"item_group"`
  PriceListRate float64 `json:"price_list_rate"`
  ActualQty     float64 `json:"actual_qty"`
  // Other fields omitted
}

// Transformed to internal model
type Product struct {
  ID         string
  CategoryID string
  Name       string
  Price      float64
  StockQty   int
}
