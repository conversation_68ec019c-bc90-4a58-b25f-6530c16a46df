.PHONY: build run migrate clean migration migration-new migration-up migration-down migration-force db-tables db-describe db-query db-migration-status db-psql db-reset redis-start redis-stop update update-app postgres-up migrate-db db-execute

include .env

build:
	docker compose build

up:
	docker compose up

migrate:
	docker compose up migrator

run:
	docker compose up app

migration:
	@read -p "Enter migration name: " name; \
	migrate create -ext sql -dir ./scripts/migrations -seq $${name}

# Migration commands
migration-new:
	@read -p "Enter migration name: " name; \
	migrate create -ext sql -dir ./scripts/migrations -seq $${name}

migration-up:
	docker compose run --rm migrator go run scripts/migrate.go

migration-down:
	docker compose run --rm migrator go run scripts/migrate.go down

migration-force:
	@read -p "Enter version to force: " version; \
	docker compose run --rm migrator go run scripts/migrate.go force $$version

# Database helper commands
db-tables:
	@docker compose exec postgres psql -U telegram -d telegram -c "\dt"

db-describe:
	@if [ -z "$(table)" ]; then \
		echo "Usage: make db-describe table=<table_name>"; \
		exit 1; \
	fi
	@docker compose exec postgres psql -U telegram -d telegram -c "SELECT column_name, data_type, character_maximum_length, is_nullable, column_default FROM information_schema.columns WHERE table_name = '$(table)' ORDER BY ordinal_position;"

db-query:
	@if [ -z "$(query)" ]; then \
		echo "Usage: make db-query query='SELECT * FROM users'"; \
		exit 1; \
	fi
	@docker compose exec postgres psql -U telegram -d telegram -c "SELECT json_agg(results) FROM ( $(query) ) results;"

db-migration-status:
	@docker compose exec postgres psql -U telegram -d telegram -c "SELECT * FROM schema_migrations"

db-psql:
	@docker compose exec postgres psql -U telegram -d telegram

db-reset:
	@echo "This will drop all tables. Are you sure? [y/N]" && read ans && [ $${ans:-N} = y ]
	@docker compose exec postgres psql -U telegram -d telegram -c "DROP SCHEMA public CASCADE; CREATE SCHEMA public;"
	@make migration-up

db-execute:
	@if [ -z "$(query)" ]; then \
		echo "Usage: make db-execute query='DELETE FROM user_warehouses WHERE id = 1;'"; \
		exit 1; \
	fi
	@docker compose exec postgres psql -U telegram -d telegram -c "$(query)"

# start and stop redis server on localhost using brew
redis-start:
	@brew services start redis

redis-stop:
	@brew services stop redis

# Update all docker containers with latest changes
update:
	@echo "Rebuilding and updating all containers..."
	docker compose down
	docker compose pull   # Pull latest images without rebuilding everything
	docker compose build --pull --no-cache=false  # Build using cache where possible
	docker compose up -d
	@echo "All containers updated successfully!"

# Update only the app container
update-app:
	@echo "Rebuilding and updating only the app container..."
	docker compose build app
	docker compose stop app
	docker compose rm -f app
	docker compose up -d app
	@echo "App container updated successfully!"

clean:
	docker compose down -v
	rm -f bot

postgres-up:
	docker compose up -d postgres

migrate-db: postgres-up
	@echo "Waiting for postgres to be ready..."
	@until docker compose exec -T postgres pg_isready -U telegram; do \
    	echo "Postgres is unavailable - sleeping"; \
    	sleep 1; \
	done
	@echo "Running migrations..."
	docker compose run --rm migrator go run scripts/migrate.go


