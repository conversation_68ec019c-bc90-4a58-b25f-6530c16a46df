package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"strconv"

	_ "github.com/lib/pq"
	"github.com/golang-migrate/migrate/v4"
	"github.com/golang-migrate/migrate/v4/database/postgres"
	_ "github.com/golang-migrate/migrate/v4/source/file"
)

func main() {
	dbURL := fmt.Sprintf("postgres://%s:%s@%s:%s/%s?sslmode=disable",
		os.<PERSON>env("POSTGRES_USER"),
		os.<PERSON>env("POSTGRES_PASSWORD"),
		os.<PERSON>env("POSTGRES_HOST"),
		os.<PERSON>env("POSTGRES_PORT"),
		os.<PERSON>env("POSTGRES_DB"),
	)

	db, err := sql.Open("postgres", dbURL)
	if err != nil {
		log.Fatal(err)
	}
	defer db.Close()

	driver, err := postgres.WithInstance(db, &postgres.Config{})
	if err != nil {
		log.Fatal(err)
	}

	m, err := migrate.NewWithDatabaseInstance(
		"file://scripts/migrations",
		"postgres",
		driver,
	)
	if err != nil {
		log.Fatal(err)
	}

	if len(os.Args) > 1 && os.Args[1] == "down" {
		if err := m.Down(); err != nil && err != migrate.ErrNoChange {
			log.Fatal(err)
		}
		log.Println("Successfully reverted migrations")
		return
	}

	if len(os.Args) > 2 && os.Args[1] == "force" {
		version, err := strconv.Atoi(os.Args[2])
		if err != nil {
			log.Fatal("Version must be an integer:", err)
		}
		if err := m.Force(version); err != nil {
			log.Fatal(err)
		}
		log.Printf("Successfully forced migration version to %d", version)
		return
	}

	if err := m.Up(); err != nil && err != migrate.ErrNoChange {
		log.Fatal(err)
	}
	log.Println("Successfully applied migrations")
}
