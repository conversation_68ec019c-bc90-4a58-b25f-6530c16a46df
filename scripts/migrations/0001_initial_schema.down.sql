-- Drop all foreign key constraints first
ALTER TABLE transactions DROP CONSTRAINT IF EXISTS transactions_user_id_fkey;
ALTER TABLE transactions DROP CONSTRAINT IF EXISTS transactions_item_id_fkey;

-- Drop indexes
DROP INDEX IF EXISTS idx_transactions_user;
DROP INDEX IF EXISTS idx_transactions_item;
DROP INDEX IF EXISTS idx_categories_parent;

DROP TABLE IF EXISTS categories;



-- Drop tables in reverse order of creation (due to foreign key constraints)
DROP TABLE IF EXISTS transactions;
DROP TABLE IF EXISTS shop_items;
DROP TABLE IF EXISTS users;
