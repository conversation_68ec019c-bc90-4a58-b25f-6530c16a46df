-- Rename phone_number to mobile
ALTER TABLE users 
    DROP COLUMN IF EXISTS phone_number CASCADE,
    ADD COLUMN IF NOT EXISTS mobile VARCHAR(20) UNIQUE,
    ADD COLUMN IF NOT EXISTS erpnext_id VARCHAR(40),
    ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true,
    ADD COLUMN IF NOT EXISTS last_login TIMESTAMP WITH TIME ZONE,
    ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE;

-- Add index for mobile lookups
CREATE INDEX IF NOT EXISTS idx_users_mobile ON users(mobile);

-- Add index for soft deletes
CREATE INDEX IF NOT EXISTS idx_users_deleted_at ON users(deleted_at);

-- Add index for active users
CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active) WHERE is_active = true;
