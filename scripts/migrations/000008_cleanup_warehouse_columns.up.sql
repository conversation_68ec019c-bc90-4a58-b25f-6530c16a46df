-- First drop the existing simple table
DROP TABLE IF EXISTS user_warehouses;

-- Create the new table with proper structure
CREATE TABLE user_warehouses (
    id SERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(telegram_id),
    warehouse_id VARCHAR(255) NOT NULL,
    is_default BOOLEAN DEFAULT false,
    assigned_by BIGINT REFERENCES users(telegram_id),
    assigned_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, warehouse_id)
);

-- Create indexes for better performance
CREATE INDEX idx_user_warehouses_user ON user_warehouses(user_id);
CREATE INDEX idx_user_warehouses_default ON user_warehouses(user_id) WHERE is_default = true;

-- Convert any existing TIMESTAMP WITH TIME ZONE columns to TIMESTAMPTZ for consistency
ALTER TABLE users 
    ALTER COLUMN created_at TYPE TIMESTAMPTZ,
    ALTER COLUMN updated_at TYPE TIMESTAMPTZ,
    ALTER COLUMN last_login TYPE TIMESTAMPTZ,
    ALTER COLUMN deleted_at TYPE TIMESTAMPTZ;

ALTER TABLE cooperatives
    ALTER COLUMN created_at TYPE TIMESTAMPTZ,
    ALTER COLUMN updated_at TYPE TIMESTAMPTZ;

-- Remove deprecated warehouse columns from users table
ALTER TABLE users
    DROP COLUMN IF EXISTS warehouse_id,
    DROP COLUMN IF EXISTS warehouse_name;
