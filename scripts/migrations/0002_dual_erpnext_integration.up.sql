-- Add ERPNext integration tables and columns

CREATE TABLE cooperatives (
    id SERIAL PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) UNIQUE NOT NULL,
    agro_erpnext_url VARCHAR(255) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

ALTER TABLE users
ADD COLUMN cooperative_id INT REFERENCES cooperatives(id),
ADD COLUMN warehouse_id VARCHAR(255),
ADD COLUMN customer_erpnext_url VARCHAR(255),
ADD COLUMN customer_api_key_encrypted BYTEA,
ADD COLUMN customer_api_secret_encrypted BYTEA;

ALTER TABLE transactions
ADD COLUMN agro_invoice_id VARCHAR(255) NOT NULL,
ADD COLUMN customer_po_id VARCHAR(255) NOT NULL,
ADD COLUMN otp VARCHAR(6),
ADD COLUMN otp_retries INT DEFAULT 0,
ADD COLUMN otp_expires_at TIMESTAMPTZ;

CREATE INDEX idx_users_cooperative ON users(cooperative_id);
CREATE INDEX idx_transactions_agro_invoice ON transactions(agro_invoice_id);
CREATE INDEX idx_transactions_customer_po ON transactions(customer_po_id);
