-- Drop indexes first
DROP INDEX IF EXISTS idx_users_verification;
DROP INDEX IF EXISTS idx_users_phone;

-- Remove unique constraint
ALTER TABLE users
DROP CONSTRAINT IF EXISTS unique_phone_number;

-- Remove columns
ALTER TABLE users
DROP COLUMN IF EXISTS verification_completed_at,
DROP COLUMN IF EXISTS verification_requested_at,
DROP COLUMN IF EXISTS verification_status,
DROP COLUMN IF EXISTS warehouse_name,
DROP COLUMN IF EXISTS is_phone_verified,
DROP COLUMN IF EXISTS phone_number;

-- Drop any orphaned tables that shouldn't exist
DROP TABLE IF EXISTS user_warehouses;
