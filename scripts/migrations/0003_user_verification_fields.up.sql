-- Add user verification and contact fields
ALTER TABLE users
ADD COLUMN phone_number <PERSON><PERSON><PERSON><PERSON>(20),
ADD COLUMN is_phone_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN warehouse_name VARCHAR(255),
ADD COLUMN verification_status VARCHAR(50) DEFAULT 'pending',
ADD COLUMN verification_requested_at TIMESTAMPTZ,
ADD COLUMN verification_completed_at TIMESTAMPTZ,
ADD CONSTRAINT unique_phone_number UNIQUE (phone_number);

-- Add index for phone number lookups
CREATE INDEX idx_users_phone ON users(phone_number);

-- Add index for verification status
CREATE INDEX idx_users_verification ON users(verification_status);
