-- Revert timestamp type changes
ALTER TABLE users 
    ALTER COLUMN created_at TYPE TIMESTAMP WITH TIME ZONE,
    ALTER COLUMN updated_at TYPE TIMESTAMP WITH TIME ZONE,
    ALTER COLUMN last_login TYPE TIMESTAMP WITH TIME ZONE,
    ALTER COLUMN deleted_at TYPE TIMESTAMP WITH TIME ZONE;

ALTER TABLE cooperatives
    ALTER COLUMN created_at TYPE TIMESTAMP WITH TIME ZONE,
    ALTER COLUMN updated_at TYPE TIMESTAMP WITH TIME ZONE;

-- Drop indexes
DROP INDEX IF EXISTS idx_user_warehouses_default;
DROP INDEX IF EXISTS idx_user_warehouses_user;

-- Drop the user_warehouses table
DROP TABLE IF EXISTS user_warehouses;

-- Create the original simple table structure
CREATE TABLE user_warehouses (
    user_id BIGINT NOT NULL,
    warehouse TEXT NOT NULL,
    PRIMARY KEY (user_id, warehouse)
);

-- Restore the original warehouse columns in users table
ALTER TABLE users
    ADD COLUMN warehouse_id VARCHAR(255),
    ADD COLUMN warehouse_name VA<PERSON>HA<PERSON>(255);
