-- Drop indexes first
DROP INDEX IF EXISTS idx_transactions_customer_po;
DROP INDEX IF EXISTS idx_transactions_agro_invoice;
DROP INDEX IF EXISTS idx_users_cooperative;

-- Drop foreign key constraints
ALTER TABLE users DROP CONSTRAINT IF EXISTS users_cooperative_id_fkey;

-- Remove columns from transactions table
ALTER TABLE transactions
DROP COLUMN IF EXISTS otp_expires_at,
DROP COLUMN IF EXISTS otp_retries,
DROP COLUMN IF EXISTS otp,
DROP COLUMN IF EXISTS customer_po_id,
DROP COLUMN IF EXISTS agro_invoice_id;

-- Remove columns from users table
ALTER TABLE users
DROP COLUMN IF EXISTS customer_api_secret_encrypted,
DROP COLUMN IF EXISTS customer_api_key_encrypted,
DROP COLUMN IF EXISTS customer_erpnext_url,
DROP COLUMN IF EXISTS warehouse_id,
DROP COLUMN IF EXISTS cooperative_id;

-- Drop cooperatives table (should be last since it's referenced by users)
DROP TABLE IF EXISTS cooperatives;

-- Drop any orphaned tables that shouldn't exist
DROP TABLE IF EXISTS user_warehouses;
