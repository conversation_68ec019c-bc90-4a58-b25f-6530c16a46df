CREATE TABLE user_warehouses (
    id SERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(telegram_id),
    warehouse_id VARCHAR(255) NOT NULL,
    is_default BOOLEAN DEFAULT false,
    assigned_by BIGINT REFERENCES users(telegram_id),
    assigned_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, warehouse_id)
);

-- Index for faster lookups
CREATE INDEX idx_user_warehouses_user ON user_warehouses(user_id);
CREATE INDEX idx_user_warehouses_default ON user_warehouses(user_id) WHERE is_default = true;