# Changelog

## [Unreleased]

### Added
- Enhanced Cart Management
  - Per-item quantity adjustment with ➕/➖ controls
  - Individual item removal functionality
  - Detailed price breakdown per item
  - Real-time subtotal calculations
  - Item-specific action buttons
  - Quick access to product details from cart

- Session Management
  - Created `SessionManager` to persist user data between bot restarts
  - Session data stored in JSON files under `./data/sessions`
  - Automatic cleanup of inactive sessions after 7 days
  - Tracks user's cart, current menu position, and selected items

- Message Handling
  - Structured `MessageHandler` for processing different message types
  - Support for commands: `/start`, `/help`
  - Support for media: photos and documents
  - Logging of text messages with username

- Shop Interface
  - Implemented interactive product browsing with categories
  - Shopping cart functionality with persistent storage
  - Quantity selection for products
  - Clean single-message interface that updates in place
  - Back navigation through menus
  - Cart operations (add, view, clear, checkout)

- ERPNext Integration
  - Configurable ERPNext client with API authentication
  - Product catalog fetching from ERPNext
  - Category/Item group support
  - Price and stock quantity tracking
  - Order creation in ERPNext

- Enhanced cart functionality with proper session management and error handling
- Improved cart display with formatted table layout and item details
- Added pagination for both categories and products (5 products, 6 categories per page)
- Enhanced category display with product counts and formatted table layout
- Added proper error handling for cart operations and quantity validation
- Improved navigation with clear back buttons and cart status
- Added refresh button when no categories are available
- Fixed special character escaping in MarkdownV2 formatting
- Added proper session initialization and management for cart operations
- Enhanced UI with emoji indicators and clearer button labels
- Added cart button with dynamic item count display
- Improved error messages with user-friendly alerts

### Changed
- Cart Interface Improvements
  - Redesigned cart display with clear item separation
  - Added emoji indicators for better visual feedback
  - Enhanced post-add-to-cart options (view cart/continue shopping)
  - Improved quantity selection with proper type handling
  - Better error handling for missing products

- Message Interface
  - Switched from multiple messages to in-place updates
  - Added callback response handling to remove loading states
  - Improved navigation with back buttons

- ERPNext Client
  - Made configuration injectable instead of environment-based
  - Added proper error handling for API responses
  - Improved URL handling with automatic /api suffix

- Improved category display layout with monospace formatting
- Enhanced product listing with better spacing and organization
- Updated button layouts for better user experience
- Modified cart display format for better readability
- Updated navigation flow between categories, products, and cart

### Fixed
- Fixed cart button functionality in category view
- Fixed pagination navigation in both category and product views
- Fixed message formatting issues with special characters
- Fixed session handling for cart operations
- Fixed cart display after adding items
- Fixed empty cart handling with proper messaging

### Technical Improvements
- Type Safety
  - Added proper type conversion handling for quantities
  - Improved error handling for session data types
  - Better validation of API responses

- Structured Handler Pattern
  - Separate handlers for messages and shop functionality
  - Clean separation of concerns between services
  - Proper error propagation

- Memory Management
  - Automatic cleanup of old sessions
  - Proper mutex usage for concurrent access
  - Memory-efficient message updates

### Code Organization
- Project Structure
  ```
  telegram-silcore/
  ├── cmd/
  │   └── bot/            # Application entry point
  ├── internal/
  │   ├── bot/
  │   │   └── handlers/   # Telegram bot handlers
  │   ├── models/         # Data models
  │   └── services/       # Business logic services
  └── data/
      └── sessions/       # Persistent session storage
  ```

### Implementation Rules
1. Session Management
   - All user data must be persisted between restarts
   - Sessions expire after 7 days of inactivity
   - Session data is stored in JSON format
   - Concurrent access is protected by mutexes

2. Message Handling
   - Use message editing instead of new messages when possible
   - Always respond to callbacks to clear loading state
   - Log all text messages with usernames
   - Keep interface clean and uncluttered

3. Shop Interface
   - Single message updates for navigation
   - Maintain breadcrumb-style navigation
   - Persist cart contents between sessions
   - Track selected quantities accurately
   - Allow individual item management in cart

4. ERPNext Integration
   - Validate all API responses
   - Handle missing data gracefully
   - Use proper error propagation
   - Support configurable endpoints

5. Code Style
   - Use proper error handling throughout
   - Implement clean interfaces between components
   - Follow Go best practices for package organization
   - Document all public functions and types

### Configuration
Required Environment Variables:
- `TELEGRAM_BOT_TOKEN`: Telegram Bot API token
- `ERPNEXT_BASE_URL`: ERPNext server URL
- `ERPNEXT_API_KEY`: ERPNext API key
- `ERPNEXT_API_SECRET`: ERPNext API secret

### Notes
- The bot uses telebot.v3 for Telegram API interaction
- All prices are displayed in KES (Kenya Shillings)
- Stock quantities are fetched real-time from ERPNext
- Cart data is persisted but cleared after successful checkout
- Cart quantities can be adjusted individually per item
- Items can be removed from cart without clearing entire cart
