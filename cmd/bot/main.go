package main

import (
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"github.com/itunza/telegram-silcore/configs"
	"github.com/itunza/telegram-silcore/internal/bot/handlers"
	"github.com/itunza/telegram-silcore/internal/config"
	"github.com/itunza/telegram-silcore/internal/services"
	"github.com/joho/godotenv"
	telebot "gopkg.in/telebot.v3"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func waitForDatabase(config *config.Config) error {
	log.Println("Waiting for database to be ready...")
	maxAttempts := 30
	for attempt := 1; attempt <= maxAttempts; attempt++ {
		db, err := gorm.Open(postgres.Open(config.GetDBConnString()), &gorm.Config{})
		if err == nil {
			sqlDB, err := db.DB()
			if err == nil {
				err = sqlDB.Ping()
				if err == nil {
					log.Println("Database connection established!")
					sqlDB.Close()
					return nil
				}
			}
		}
		log.Printf("Database connection attempt %d/%d failed: %v", attempt, maxAttempts, err)
		time.Sleep(2 * time.Second)
	}
	return fmt.Errorf("failed to connect to database after %d attempts", maxAttempts)
}

func waitForRedis(redisURL string) error {
	log.Println("Waiting for Redis to be ready...")
	maxAttempts := 30
	for attempt := 1; attempt <= maxAttempts; attempt++ {
		_, err := services.NewSessionService(redisURL)
		if err == nil {
			log.Println("Redis connection established!")
			return nil
		}
		log.Printf("Redis connection attempt %d/%d failed: %v", attempt, maxAttempts, err)
		time.Sleep(2 * time.Second)
	}
	return fmt.Errorf("failed to connect to Redis after %d attempts", maxAttempts)
}

func main() {
	log.Println("=== Starting Telegram Silcore Bot ===")
	log.Printf("Starting time: %v", time.Now().Format(time.RFC3339))

	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Printf("Warning: Error loading .env file: %v", err)
	}

	// Get environment variables
	log.Println("Loading environment variables...")
	botToken := os.Getenv("TELEGRAM_BOT_TOKEN")
	if botToken == "" {
		log.Fatal("TELEGRAM_BOT_TOKEN environment variable is required")
	}
	log.Println("Bot token loaded successfully")

	redisURL := os.Getenv("REDIS_URL")
	if redisURL == "" {
		redisURL = "redis://localhost:6379/0"
		log.Printf("Using default Redis URL: %s", redisURL)
	}

	// Load configuration
	log.Println("Loading application configuration...")
	config, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}
	log.Println("Configuration loaded successfully")

	// Wait for services to be ready
	if err := waitForDatabase(config); err != nil {
		log.Fatalf("Database initialization failed: %v", err)
	}

	if err := waitForRedis(redisURL); err != nil {
		log.Fatalf("Redis initialization failed: %v", err)
	}

	// Initialize database connection
	log.Println("Initializing database connection...")
	db, err := gorm.Open(postgres.Open(config.GetDBConnString()), &gorm.Config{})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	log.Println("Database connection initialized successfully")

	sqlDB, err := db.DB()
	if err != nil {
		log.Fatalf("Failed to get database instance: %v", err)
	}
	defer sqlDB.Close()

	// Set connection pool settings
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)

	// Initialize services
	log.Println("Initializing services...")

	log.Println("Creating session service...")
	sessionService, err := services.NewSessionService(redisURL)
	if err != nil {
		log.Fatalf("Failed to create session service: %v", err)
	}
	defer sessionService.Close()

	log.Println("Loading MPesa configuration...")
	mpesaConfig := configs.LoadMPesaConfig()
	mpesaClient := services.NewMPesaClient(mpesaConfig)

	log.Println("Initializing ERPNext client...")
	erpnextClient, err := services.NewERPNextClient(
		config.ERPNext.BaseURL,
		config.ERPNext.APIKey,
		config.ERPNext.APISecret,
	)
	if err != nil {
		log.Fatalf("Failed to create ERPNext client: %v", err)
	}

	log.Println("Initializing service layer...")
	authService := services.NewAuthService(db, sessionService, erpnextClient)
	productCache, err := services.NewProductCache(redisURL, 30*time.Minute)
	if err != nil {
		log.Fatalf("Failed to create product cache: %v", err)
	}
	defer productCache.Close()

	shopService, err := services.NewShopService(erpnextClient, redisURL, config)
	if err != nil {
		log.Fatalf("Failed to create shop service: %v", err)
	}

	orderService := services.NewOrderService(erpnextClient, shopService)

	paymentService, err := services.NewPaymentService(
		db,
		erpnextClient,
		mpesaClient,
		orderService,
		redisURL,
		sessionService,
	)
	if err != nil {
		log.Fatalf("Failed to create payment service: %v", err)
	}
	// Set the payment service reference for the MPesa client
	services.SetPaymentService(paymentService)

	// Initialize bot
	log.Println("Initializing Telegram bot...")
	pref := telebot.Settings{
		Token:  botToken,
		Poller: &telebot.LongPoller{Timeout: 10 * time.Second},
		OnError: func(err error, c telebot.Context) {
			log.Printf("Telegram error: %v", err)
		},
	}

	b, err := telebot.NewBot(pref)
	if err != nil {
		log.Fatalf("Failed to create bot: %v", err)
	}

	// Set the bot instance in the payment service

	services.SetGlobalPaymentService(paymentService)
	paymentService.SetBot(b)

	// Initialize webhook service for payment callbacks
	log.Println("Initializing webhook service...")
	webhookService, err := services.NewWebhookService(paymentService, redisURL)
	if err != nil {
		log.Fatalf("Failed to create webhook service: %v", err)
	}
	defer webhookService.Close()

	// Start webhook server on port 8080 (or use a configurable port)
	webhookPort := os.Getenv("WEBHOOK_PORT")
	if webhookPort == "" {
		webhookPort = "8080"
	}
	log.Printf("Starting webhook server on port %s", webhookPort)
	go webhookService.StartServer(webhookPort)

	mpesaCallbackURL := os.Getenv("MPESA_CALLBACK_URL")
	if mpesaCallbackURL == "" {
		log.Println("Warning: MPESA_CALLBACK_URL environment variable not set.")
	} else {
		log.Printf("MPESA Callback URL: %s", mpesaCallbackURL)
	}

	// Initialize handlers
	log.Println("Initializing bot handlers...")
	loginHandler := handlers.NewLoginHandler(authService)
	shopHandler := handlers.NewShopHandler(shopService, sessionService, authService, paymentService)
	paymentHandler := handlers.NewPaymentHandler(paymentService, sessionService)
	messageHandler := handlers.NewMessageHandler(sessionService, shopService, shopHandler, paymentHandler)
	warehouseHandler := handlers.NewWarehouseHandler(shopService, authService)
	checkoutHandler := handlers.NewCheckoutHandler(shopService, sessionService, authService, orderService, paymentService)

	// Register handlers
	log.Println("Registering bot handlers...")
	b.Handle("/start", messageHandler.HandleStart)
	b.Handle("/warehouse", warehouseHandler.HandleWarehouseCommand)
	b.Handle("/pay", paymentHandler.HandlePayCommand)

	loginHandler.RegisterHandlers(b)
	shopHandler.RegisterHandlers(b)
	messageHandler.RegisterHandlers(b)
	checkoutHandler.RegisterHandlers(b)
	paymentHandler.RegisterHandlers(b)
	warehouseHandler.RegisterHandlers(b)

	log.Println("=== Bot initialization completed ===")
	log.Println("Starting bot poller...")
	// Create a combined callback handler
	b.Handle(telebot.OnCallback, func(c telebot.Context) error {
		data := c.Callback().Data
		log.Printf("Received callback: %s", data)

		// Check if it's a warehouse-related callback
		if strings.HasPrefix(data, "wh:") {
			return warehouseHandler.HandleCallback(c)
		}

		// Check if it's a login-related callback
		if data == "login" {
			return loginHandler.HandleCallback(c)
		}

		// Check if it's a payment-related callback
		if strings.HasPrefix(data, "pay:") || strings.HasPrefix(data, "payment:") {
			return paymentHandler.HandleCallback(c)
		}

		// Check if it's a checkout-related callback
		if strings.HasPrefix(data, "checkout:") {
			return checkoutHandler.HandleCallback(c)
		}

		// All other callbacks go to shop handler
		return shopHandler.HandleCallback(c)
	})
	b.Start()
}
