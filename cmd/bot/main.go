package main

import (
	"log"
	"os"
	"time"

	"github.com/itunza/telegram-silcore/internal/bot/handlers"
	"github.com/itunza/telegram-silcore/internal/services"
	"github.com/joho/godotenv"
	telebot "gopkg.in/telebot.v3"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Printf("Error loading .env file: %v", err)
	}

	// Get environment variables
	botToken := os.Getenv("TELEGRAM_BOT_TOKEN")
	if botToken == "" {
		log.Fatal("TELEGRAM_BOT_TOKEN environment variable is required")
	}

	redisURL := os.Getenv("REDIS_URL")
	if redisURL == "" {
		redisURL = "redis://localhost:6379/0" // Default Redis URL
	}

	// Initialize services
	sessionService, err := services.NewSessionService(redisURL)
	if err != nil {
		log.Fatalf("Failed to create session service: %v", err)
	}
	defer sessionService.Close()

	productCache, err := services.NewProductCache(redisURL, 5*time.Minute)
	if err != nil {
		log.Fatalf("Failed to create product cache: %v", err)
	}
	defer productCache.Close()

	// Initialize ERPNext client
	erpnext, err := services.NewERPNextClient(
		os.Getenv("ERPNEXT_BASE_URL"),
		os.Getenv("ERPNEXT_API_KEY"),
		os.Getenv("ERPNEXT_API_SECRET"),
	)
	if err != nil {
		log.Fatalf("Failed to create ERPNext client: %v", err)
	}

	// Initialize shop service with cache
	shopService, err := services.NewShopService(erpnext, redisURL)
	if err != nil {
		log.Fatalf("Failed to create shop service: %v", err)
	}

	// Initialize bot
	pref := telebot.Settings{
		Token:  botToken,
		Poller: &telebot.LongPoller{Timeout: 10 * time.Second},
	}

	b, err := telebot.NewBot(pref)
	if err != nil {
		log.Fatal(err)
	}

	// Initialize handlers
	// Note: CartHandler is now compatible with shopService and sessionService.
	cartHandler := handlers.NewCartHandler(shopService, sessionService)
	productHandler := handlers.NewProductHandler(shopService, sessionService) // Initialize ProductHandler
	categoryHandler := handlers.NewCategoryHandler(shopService, sessionService) // Initialize CategoryHandler
	shopHandler := handlers.NewShopHandler(shopService, sessionService, cartHandler, productHandler, categoryHandler) // Pass all handlers
	messageHandler := handlers.NewMessageHandler()

	// Register handlers
	// ShopHandler's RegisterHandlers method already sets up bot.Handle(telebot.OnCallback, h.HandleCallback)
	// which will now delegate "cart:", "product:", "qty:", "page:cat:", "catpage:" prefixed callbacks to appropriate handlers.
	shopHandler.RegisterHandlers(b)
	messageHandler.RegisterHandlers(b)

	log.Println("Bot started...")
	b.Start()
}
