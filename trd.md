# ERPNext Telegram Bot Technical Specification

## Overview
A Telegram bot that serves as a point of sale interface for ERPNext, featuring user authentication, shopping cart functionality, payment processing (M-Pesa and on account), and receipt generation.

## System Architecture

### Components
1. Telegram Bot (Go/telebot)
2. Database (PostgreSQL)
3. ERPNext Integration Service
4. Payment Processing Service
5. SMS Service
6. PDF Generation Service

### Database Schema

```sql
-- Enhanced Users table
CREATE TABLE users (
    telegram_id BIGINT PRIMARY KEY,
    erpnext_token TEXT,
    token_expiry TIMESTAMP,
    customer_code TEXT,
    preferred_language TEXT DEFAULT 'en',
    last_active TIMESTAMP,
    notification_preferences JSONB
);

-- Wishlist table
CREATE TABLE wishlists (
    wishlist_id SERIAL PRIMARY KEY,
    telegram_id BIGINT REFERENCES users(telegram_id),
    item_code TEXT,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Order History
CREATE TABLE orders (
    order_id SERIAL PRIMARY KEY,
    telegram_id BIGINT REFERENCES users(telegram_id),
    invoice_number TEXT,
    total_amount DECIMAL(10,2),
    payment_method TEXT,
    status TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP
);

-- Product Reviews
CREATE TABLE reviews (
    review_id SERIAL PRIMARY KEY,
    telegram_id BIGINT REFERENCES users(telegram_id),
    item_code TEXT,
    rating INTEGER CHECK (rating BETWEEN 1 AND 5),
    review_text TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Promotional Codes
CREATE TABLE promo_codes (
    code TEXT PRIMARY KEY,
    discount_type TEXT,
    discount_value DECIMAL(10,2),
    valid_from TIMESTAMP,
    valid_until TIMESTAMP,
    usage_limit INTEGER,
    current_usage INTEGER DEFAULT 0
);

-- Previous schema tables...

```sql
-- Users table for storing authentication tokens
CREATE TABLE users (
    telegram_id BIGINT PRIMARY KEY,
    erpnext_token TEXT,
    token_expiry TIMESTAMP,
    customer_code TEXT
);

-- Shopping cart table
CREATE TABLE shopping_carts (
    cart_id SERIAL PRIMARY KEY,
    telegram_id BIGINT REFERENCES users(telegram_id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status TEXT DEFAULT 'active'
);

-- Cart items table
CREATE TABLE cart_items (
    item_id SERIAL PRIMARY KEY,
    cart_id INTEGER REFERENCES shopping_carts(cart_id),
    item_code TEXT,
    quantity INTEGER,
    price DECIMAL(10,2)
);
```

## Additional Features

### 1. Inventory Management
- Real-time stock checking before order confirmation
- Low stock alerts for administrators
- Reserved stock handling during checkout process
- Batch/serial number tracking for applicable items

### 2. Customer Experience
- Product recommendations based on purchase history
- Wishlist functionality
- Order history viewing
- Custom product alerts/notifications
- Rating and review system after purchase

### 3. Analytics Dashboard
- Sales performance metrics
- Popular products tracking
- Customer behavior analysis
- Payment method usage statistics
- Peak usage time tracking

### 4. Advanced Cart Features
- Saved cart functionality
- Bulk order processing
- Promotional code support
- Dynamic pricing rules
- Bundle product support

### 5. Multi-Language Support
- Dynamic language switching
- Localized product descriptions
- Currency conversion support
- Regional tax handling

## Core Features

### 1. User Authentication
- Command: `/login`
- Flow:
  1. Request ERPNext credentials
  2. Validate against ERPNext API
  3. Store authentication token in database
  4. Associate Telegram ID with ERPNext customer code

### 2. Product Catalog
- Command: `/products [category]`
- Features:
  - List available products with prices
  - Show product images if available
  - Filter by category
  - Pagination support

### 3. Shopping Cart
- Commands:
  - `/cart` - View current cart
  - `/add <item_code> <quantity>` - Add item to cart
  - `/remove <item_code>` - Remove item from cart
  - `/clear` - Clear cart
  - `/checkout` - Proceed to checkout

### 4. Payment Processing
- Commands:
  - `/pay mpesa` - Initiate M-Pesa STK push
  - `/pay account` - Process payment on account
- Features:
  - M-Pesa integration using Daraja API
  - Account payment verification against ERPNext
  - Payment status tracking

### 5. Receipt Generation
- Automatic generation after successful payment
- Features:
  - SMS receipt via Africa's Talking API
  - PDF receipt generation
  - QR code for digital copy

## Implementation Plan

### Phase 1: Basic Setup
1. Set up project structure
2. Implement basic bot commands
3. Create database schema
4. Set up ERPNext API integration

### Phase 2: Core Features
1. Implement user authentication
2. Build product catalog browsing
3. Develop shopping cart functionality
4. Create basic checkout flow

### Phase 3: Payment Integration
1. Implement M-Pesa integration
2. Add account payment support
3. Set up payment verification

### Phase 4: Receipt System
1. Implement SMS integration
2. Create PDF generation
3. Add QR code functionality

## Code Structure

```go
package main

import (
    "database/sql"
    "encoding/json"
    "log"
    "time"
    
    telebot "gopkg.in/telebot.v3"
    _ "github.com/lib/pq"
)

// Bot configuration
type Config struct {
    TelegramToken string
    ERPNextURL    string
    DatabaseURL   string
    MPesaConfig   MPesaConfig
}

// Bot service
type Bot struct {
    bot      *telebot.Bot
    db       *sql.DB
    erpnext  *ERPNextClient
    payments *PaymentService
    receipts *ReceiptService
}

// Main service interfaces
type ERPNextClient interface {
    Authenticate(username, password string) (string, error)
    GetProducts() ([]Product, error)
    CreateSalesInvoice(cart Cart) (string, error)
}

type PaymentService interface {
    InitiateMPesa(phone string, amount float64) (string, error)
    ProcessAccountPayment(customerCode string, amount float64) (string, error)
}

type ReceiptService interface {
    SendSMS(phone, message string) error
    GeneratePDF(invoice Invoice) ([]byte, error)
}
```

## Project Structure

```
telegram-silcore/
├── cmd/                    # Main applications for this project
│   └── bot/               # The bot application
│       └── main.go        # Entry point for the bot
├── internal/              # Private application and library code
│   ├── bot/              # Bot-specific logic
│   │   ├── handlers/     # Message and command handlers
│   │   └── middleware/   # Bot middleware functions
│   ├── config/           # Configuration handling
│   └── models/           # Data models
├── pkg/                   # Library code that could be used by external applications
│   ├── telegram/         # Telegram-specific utilities
│   └── utils/            # General utilities
├── scripts/              # Scripts for development and deployment
├── configs/              # Configuration files
├── docs/                 # Documentation files
├── test/                 # Additional test applications and test data
├── .gitignore           # Git ignore file
├── go.mod               # Go module file
├── go.sum               # Go module checksum
├── README.md            # Project documentation
└── trd.md               # Technical Requirements Document
```

### Directory Descriptions

- `cmd/`: Contains the main applications for the project. The bot's entry point will be here.
- `internal/`: Private application code that won't be imported by other projects.
- `pkg/`: Library code that could be used by external applications.
- `scripts/`: Contains various scripts for development, building, testing, and deployment.
- `configs/`: Configuration files for different environments.
- `docs/`: Project documentation.
- `test/`: Additional test files and test data.

## Performance Optimization

1. **Caching Strategy**
   - Redis implementation for frequent queries
   - Product catalog caching
   - User session caching
   - Cart state caching

2. **Query Optimization**
   - Indexed database fields
   - Query result pagination
   - Lazy loading for product images
   - Database connection pooling

3. **Load Balancing**
   - Multiple bot instances support
   - Database read replicas
   - Distributed caching
   - Rate limiting implementation

## Monitoring and Analytics

1. **System Monitoring**
   - Prometheus metrics collection
   - Grafana dashboards
   - Alert system for system issues
   - Performance bottleneck detection

2. **Business Analytics**
   - Sales performance tracking
   - User behavior analysis
   - Conversion rate monitoring
   - A/B testing capability

3. **Error Tracking**
   - Detailed error logging
   - Error pattern analysis
   - Automated error reporting
   - Recovery mechanism monitoring

## Disaster Recovery

1. **Backup Strategy**
   - Automated daily backups
   - Point-in-time recovery
   - Cross-region replication
   - Backup verification system

2. **Failover Planning**
   - High availability setup
   - Automatic failover mechanisms
   - Data consistency checking
   - Service recovery procedures

3. **Data Retention**
   - Compliance with data protection laws
   - Automated data archiving
   - Data cleanup procedures
   - Audit trail maintenance

## Security Considerations

1. **Authentication**
   - Secure storage of ERPNext tokens
   - Regular token rotation
   - Session timeout handling

2. **Data Protection**
   - Encryption of sensitive data
   - Secure database connections
   - Input validation

3. **Payment Security**
   - Payment verification
   - Transaction logging
   - Error handling and rollback

## Error Handling

1. **Network Errors**
   - Retry mechanisms for API calls
   - Fallback options for payment processing
   - User notification on system issues

2. **Data Validation**
   - Input sanitization
   - Quantity and price verification
   - Stock availability checking

3. **Payment Errors**
   - Payment failure handling
   - Refund process
   - Transaction logging

## Testing Strategy

1. **Unit Tests**
   - Command handlers
   - Database operations
   - Service integrations

2. **Integration Tests**
   - ERPNext API integration
   - Payment processing
   - Receipt generation

3. **End-to-End Tests**
   - Complete purchase flow
   - Error scenarios
   - Performance testing

## Deployment Considerations

1. **Infrastructure**
   - Container-based deployment
   - Database backups
   - Monitoring setup

2. **Configuration**
   - Environment variables
   - API keys management
   - Logging setup

3. **Maintenance**
   - Database maintenance
   - Log rotation
   - Performance monitoring