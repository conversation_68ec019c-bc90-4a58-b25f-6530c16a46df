package types

type Customer struct {
	ID           string               `json:"id"`
	Cart         map[string]*CartItem `json:"cart"`
	CustomerCode string               `json:"customer_code"`
	Mobile       string               `json:"mobile"`
	FullName     string               `json:"full_name"`
	// other fields as needed
}

type CartItem struct {
	ProductID string  `json:"product_id"`
	Quantity  int     `json:"quantity"`
	Price     float64 `json:"price"`
	Warehouse string  `json:"warehouse"`
}
