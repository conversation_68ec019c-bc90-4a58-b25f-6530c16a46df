package config

import (
	"fmt"
	"os"
	"time"

	"gopkg.in/telebot.v3"
)

// Config holds all configuration for the application
type Config struct {
	TelegramToken string
	Debug         bool
}

// Load creates a new Config instance from environment variables
func Load() (*Config, error) {
	token := os.Getenv("TELEGRAM_BOT_TOKEN")
	if token == "" {
		return nil, fmt.Errorf("TELEGRAM_BOT_TOKEN environment variable is required")
	}

	return &Config{
		TelegramToken: token,
		Debug:         os.Getenv("DEBUG") == "true",
	}, nil
}

// BotSettings returns telebot Settings based on the config
func (c *Config) BotSettings() telebot.Settings {
	return telebot.Settings{
		Token:  c.TelegramToken,
		Poller: &telebot.LongPoller{Timeout: 10 * time.Second},
		OnError: func(err error, ctx telebot.Context) {
			if c.Debug {
				fmt.Printf("Error: %v\n", err)
			}
		},
	}
}
