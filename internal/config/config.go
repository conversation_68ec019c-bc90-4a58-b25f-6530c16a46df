package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	telebot "gopkg.in/telebot.v3"
	"gopkg.in/yaml.v3"
)

// Config holds application configuration
type Config struct {
	TelegramToken string
	Debug         bool

	// Database settings
	DBHost     string
	DBPort     int
	DBUser     string
	DBPassword string
	DBName     string

	// ERPNext settings
	ERPNext ERPNextConfig
}

// ERPNextConfig holds ERPNext specific configuration
type ERPNextConfig struct {
	BaseURL        string
	APIKey         string
	APISecret      string
	DefaultCompany string
	Warehouses     []string
	PriceList      string
	AccountsConfig AccountsConfig
}

// AccountsConfig holds ERPNext accounting configuration
type AccountsConfig struct {
	DefaultReceivableAccount string
	DefaultPayableAccount    string
	DefaultIncomeAccount     string
	DefaultCashAccount       string
}

// Load loads configuration from environment variables
func Load() (*Config, error) {
	dbPort, err := strconv.Atoi(os.Getenv("POSTGRES_PORT"))
	if err != nil {
		dbPort = 5432 // default postgres port
	}

	return &Config{
		TelegramToken: os.Getenv("TELEGRAM_BOT_TOKEN"),
		Debug:         os.Getenv("DEBUG") == "true",
		DBHost:        os.Getenv("POSTGRES_HOST"),
		DBPort:        dbPort,
		DBUser:        os.Getenv("POSTGRES_USER"),
		DBPassword:    os.Getenv("POSTGRES_PASSWORD"),
		DBName:        os.Getenv("POSTGRES_DB"),
		ERPNext: ERPNextConfig{
			BaseURL:        os.Getenv("ERPNEXT_BASE_URL"),
			APIKey:         os.Getenv("ERPNEXT_API_KEY"),
			APISecret:      os.Getenv("ERPNEXT_API_SECRET"),
			DefaultCompany: os.Getenv("ERPNEXT_DEFAULT_COMPANY"),
			Warehouses:     strings.Split(os.Getenv("ERPNEXT_WAREHOUSES"), ","),
			PriceList:      os.Getenv("ERPNEXT_PRICE_LIST"),
			AccountsConfig: AccountsConfig{
				DefaultReceivableAccount: os.Getenv("ERPNEXT_DEFAULT_RECEIVABLE_ACCOUNT"),
				DefaultPayableAccount:    os.Getenv("ERPNEXT_DEFAULT_PAYABLE_ACCOUNT"),
				DefaultIncomeAccount:     os.Getenv("ERPNEXT_DEFAULT_INCOME_ACCOUNT"),
				DefaultCashAccount:       os.Getenv("ERPNEXT_DEFAULT_CASH_ACCOUNT"),
			},
		},
	}, nil
}

// GetDBConnString returns the database connection string for GORM
func (c *Config) GetDBConnString() string {
	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=disable",
		c.DBHost, c.DBPort, c.DBUser, c.DBPassword, c.DBName)
}

// BotSettings returns telebot settings
func (c *Config) BotSettings() telebot.Settings {
	return telebot.Settings{
		Token:  c.TelegramToken,
		Poller: &telebot.LongPoller{Timeout: 10 * time.Second},
		OnError: func(err error, ctx telebot.Context) {
			if c.Debug {
				fmt.Printf("Error: %v\n", err)
			}
		},
	}
}

// LoadYAMLConfig loads configuration from YAML file
func LoadYAMLConfig(configPath string) (*Config, error) {
	// First load environment variables
	config, err := Load()
	if err != nil {
		return nil, err
	}

	// If config path is provided, load and merge YAML config
	if configPath != "" {
		data, err := os.ReadFile(filepath.Clean(configPath))
		if err != nil {
			return nil, fmt.Errorf("error reading config file: %w", err)
		}

		var yamlConfig struct {
			ERPNext ERPNextConfig `yaml:"erpnext"`
		}

		if err := yaml.Unmarshal(data, &yamlConfig); err != nil {
			return nil, fmt.Errorf("error parsing config file: %w", err)
		}

		// Merge YAML config with environment variables (environment variables take precedence)
		if config.ERPNext.BaseURL == "" {
			config.ERPNext.BaseURL = yamlConfig.ERPNext.BaseURL
		}
		if config.ERPNext.APIKey == "" {
			config.ERPNext.APIKey = yamlConfig.ERPNext.APIKey
		}
		if config.ERPNext.APISecret == "" {
			config.ERPNext.APISecret = yamlConfig.ERPNext.APISecret
		}
		if config.ERPNext.DefaultCompany == "" {
			config.ERPNext.DefaultCompany = yamlConfig.ERPNext.DefaultCompany
		}
		if len(config.ERPNext.Warehouses) == 0 {
			config.ERPNext.Warehouses = yamlConfig.ERPNext.Warehouses
		}
		if config.ERPNext.PriceList == "" {
			config.ERPNext.PriceList = yamlConfig.ERPNext.PriceList
		}
		// Merge accounts config
		if config.ERPNext.AccountsConfig.DefaultReceivableAccount == "" {
			config.ERPNext.AccountsConfig.DefaultReceivableAccount = yamlConfig.ERPNext.AccountsConfig.DefaultReceivableAccount
		}
		// ... merge other account settings
	}

	return config, nil
}
