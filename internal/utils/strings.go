package utils

import "strings"

// SanitizePhone removes any non-numeric characters from phone number
// and ensures it starts with country code (254 for Kenya)
// Accepts formats: 254XXXXXXXXX, 07XXXXXXXX, 01XXXXXXXX
func SanitizePhone(phone string) string {
    // Remove any non-numeric characters
    clean := strings.Map(func(r rune) rune {
        if r >= '0' && r <= '9' {
            return r
        }
        return -1
    }, phone)

    // Handle different formats
    switch {
    case strings.HasPrefix(clean, "254"):
        // Already in correct format
        if len(clean) != 12 {
            return ""
        }
        return clean
    case strings.HasPrefix(clean, "0"):
        // Convert 07XXXXXXXX or 01XXXXXXXX to 254XXXXXXXXX
        if len(clean) != 10 {
            return ""
        }
        return "254" + clean[1:]
    case len(clean) == 9:
        // Handle case where number is provided without prefix
        return "254" + clean
    default:
        return ""
    }
}
