package handlers

import (
	"fmt"
	"log"

	"gopkg.in/telebot.v3"
)

// MessageHandler handles general message commands
type MessageHand<PERSON> struct{}

// NewMessageHandler creates a new MessageHandler instance
func NewMessageHandler() *MessageHandler {
	return &MessageHandler{}
}

// RegisterHandlers registers all message handlers
func (h *MessageHandler) RegisterHandlers(bot *telebot.Bot) {
	// Handle /start command
	bot.Handle("/start", h.handleStart)

	// Handle /help command
	bot.Handle("/help", h.handleHelp)

	// Handle text messages
	bot.Handle(telebot.OnText, h.handleText)

	// Handle photos
	bot.Handle(telebot.OnPhoto, h.handlePhoto)

	// Handle documents
	bot.Handle(telebot.OnDocument, h.handleDocument)
}

// handleStart handles the /start command
func (h *MessageHandler) handleStart(c telebot.Context) error {
	msg := "Welcome to Silcore Bot! 👋\n\n" +
		"Available commands:\n" +
		"/shop - Browse our shop\n" +
		"/help - Show this help message"
	return c.Send(msg)
}

// handleHelp handles the /help command
func (h *MessageHandler) handleHelp(c telebot.Context) error {
	return c.Send("Available commands:\n" +
		"/shop - Browse our shop\n" +
		"/help - Show this help message")
}

// handleText handles text messages
func (h *MessageHandler) handleText(c telebot.Context) error {
	log.Printf("[%s] %s", c.Sender().Username, c.Text())
	return c.Send(fmt.Sprintf("I received your message: %s\nUse /help to see available commands.", c.Text()))
}

// handlePhoto handles photo messages
func (h *MessageHandler) handlePhoto(c telebot.Context) error {
	return c.Send("Nice photo! 📸")
}

// handleDocument handles document messages
func (h *MessageHandler) handleDocument(c telebot.Context) error {
	return c.Send("Thanks for the document! 📄")
}
