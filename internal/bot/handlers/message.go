package handlers

import (
	"fmt"
	"log"

	"github.com/itunza/telegram-silcore/internal/services"
	"gopkg.in/telebot.v3"
)

// MessageHandler handles general message commands
type MessageHandler struct {
	sessionService *services.SessionService
	shopService    *services.ShopService
	shopHandler    *ShopHandler
	paymentHandler *PaymentHandler
}

// NewMessageHandler creates a new MessageHandler instance
func NewMessageHandler(
	sessionService *services.SessionService,
	shopService *services.ShopService,
	shopHandler *ShopHandler,
	paymentHandler *PaymentHandler,
) *MessageHandler {
	return &MessageHandler{
		sessionService: sessionService,
		shopService:    shopService,
		shopHandler:    shopHandler,
		paymentHandler: paymentHandler,
	}
}

// RegisterHandlers registers all message handlers
func (h *MessageHandler) RegisterHandlers(bot *telebot.Bot) {
	// Handle /start command
	bot.Handle("/start", h.HandleStart)

	// Handle /help command
	bot.Handle("/help", h.<PERSON>le<PERSON>elp)

	// Handle text messages
	bot.Handle(telebot.OnText, h.handleText)

	// Handle photos
	bot.Handle(telebot.OnPhoto, h.handlePhoto)

	// Handle documents
	bot.Handle(telebot.OnDocument, h.handleDocument)
}

// HandleStart handles the /start command
func (h *MessageHandler) HandleStart(c telebot.Context) error {
	session := h.sessionService.GetSession(c.Sender().ID)

	if session.LocalUserID != 0 && session.ERPNextUser != "" {
		msg := "Welcome back! \n\nUse /shop to browse or /help for assistance."
		return c.Send(msg)
	}

	msg := `Welcome to our shop! 

To get started:
1. Use /login to verify your account
2. Browse products with /shop
3. Use /help for assistance

Need help? Contact our support.`

	return c.Send(msg)
}

// HandleHelp handles the /help command
func (h *MessageHandler) HandleHelp(c telebot.Context) error {
	helpText := `Available commands:
/start - Start or restart the bot
/login - Login to your account
/shop - Browse our products
/cart - View your shopping cart
/logout - Logout from your account
/customers - Manage your customers
/checkout - Proceed to checkout
/help - Show this help message

Need assistance? Contact our support team.`

	return c.Send(helpText)
}

// handleText handles text messages
func (h *MessageHandler) handleText(c telebot.Context) error {
	log.Printf("[%s] %s", c.Sender().Username, c.Text())

	// Check session state
	session := h.sessionService.GetSession(c.Sender().ID)

	switch {
	case session.CurrentMenu == "payment_mpesa":
		log.Printf("Handling M-Pesa phone number input")
		// Delegate to payment handler for M-Pesa phone number
		return h.paymentHandler.HandleMPesaPhoneNumber(c)

	case session.AwaitingSearch:
		log.Printf("Handling search")
		// Delegate to shop handler

		return h.shopHandler.handleText(c)

	default:
		log.Printf("Handling default")
		return c.Send(fmt.Sprintf("I received your message: %s\nUse /help to see available commands.", c.Text()))
	}
}

// handlePhoto handles photo messages
func (h *MessageHandler) handlePhoto(c telebot.Context) error {
	return c.Send("Nice photo! ")
}

// handleDocument handles document messages
func (h *MessageHandler) handleDocument(c telebot.Context) error {
	return c.Send("Thanks for the document! ")
}
