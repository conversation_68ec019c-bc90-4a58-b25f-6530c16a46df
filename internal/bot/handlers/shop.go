package handlers

import (
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	"github.com/itunza/telegram-silcore/internal/services"
	"gopkg.in/telebot.v3"
)

// ShopHandler handles shop-related commands
type ShopHandler struct {
	shopService    *services.ShopService
	sessions       *services.SessionService
	authService    *services.AuthService
	paymentService *services.PaymentService
}

// NewShopHandler creates a new ShopHandler instance
func NewShopHandler(shopService *services.ShopService, sessions *services.SessionService, authService *services.AuthService, paymentService *services.PaymentService) *ShopHandler {
	return &ShopHandler{
		shopService: shopService,
		sessions:    sessions,
		authService: authService,
		// Add payment service if needed
		paymentService: services.GetPaymentService(),
	}
}

// RegisterHandlers registers all shop-related handlers
func (h *ShopHandler) RegisterHandlers(bot *telebot.Bot) {
	bot.Handle("/shop", h.<PERSON>le)
	bot.Handle("/customers", h.HandleCustomers)
	bot.Handle(telebot.OnCallback, h.HandleCallback)
	bot.Handle(telebot.OnText, h.handleText)
}

// Handle handles the /shop command
func (h *ShopHandler) Handle(c telebot.Context) error {
	// Validate session first
	if err := h.validateSession(c); err != nil {
		// Send login requirement message
		_, err := c.Bot().Send(
			c.Sender(),
			"⚠️ Please log in first using /login to access the shop.",
			&telebot.ReplyMarkup{InlineKeyboard: [][]telebot.InlineButton{
				{{Text: "🔑 Login", Data: "login"}},
			}},
		)
		return err
	}

	// Get or create session
	session := h.sessions.GetSession(c.Sender().ID)

	// Initialize customers map if nil
	if session.Customers == nil {
		session.Customers = make(map[string]*services.CustomerSession)
	}

	// Create default customer if no customers exist
	if len(session.Customers) == 0 {
		defaultCustomer := &services.CustomerSession{
			CustomerID: "default",
			Cart:       make(map[string]*services.CartItem),
			CreatedAt:  time.Now(),
			LastActive: time.Now(),
		}
		session.Customers["default"] = defaultCustomer
		session.ActiveCustomerID = "default"
		if err := h.sessions.SaveSession(session); err != nil {
			log.Printf("Error saving session after creating default customer: %v", err)
			return err
		}
	}

	// Ensure active customer is set
	if session.ActiveCustomerID == "" {
		for id := range session.Customers {
			session.ActiveCustomerID = id
			if err := h.sessions.SaveSession(session); err != nil {
				log.Printf("Error saving session after setting active customer: %v", err)
				return err
			}
			break
		}
	}

	return h.showCategories(c)
}

// HandleCustomers handles the /customers command
func (h *ShopHandler) HandleCustomers(c telebot.Context) error {
	// Validate session first
	if err := h.validateSession(c); err != nil {
		return c.Send(
			"⚠️ Please log in first using /login to manage customers.",
			&telebot.ReplyMarkup{InlineKeyboard: [][]telebot.InlineButton{
				{{Text: "🔑 Login", Data: "login"}},
			}},
		)
	}

	// Get or create session
	session := h.sessions.GetSession(c.Sender().ID)

	// Initialize customers map if nil
	if session.Customers == nil {
		session.Customers = make(map[string]*services.CustomerSession)
	}

	// Create default customer if no customers exist
	if len(session.Customers) == 0 {
		defaultID := "default"
		session.Customers[defaultID] = &services.CustomerSession{
			CustomerID: defaultID,
			Cart:       make(map[string]*services.CartItem),
			CreatedAt:  time.Now(),
			LastActive: time.Now(),
		}
		session.ActiveCustomerID = defaultID
		if err := h.sessions.SaveSession(session); err != nil {
			log.Printf("Error saving session after creating default customer: %v", err)
		}
	}

	// Ensure active customer is set
	if session.ActiveCustomerID == "" {
		for id := range session.Customers {
			session.ActiveCustomerID = id
			if err := h.sessions.SaveSession(session); err != nil {
				log.Printf("Error saving session after setting active customer: %v", err)
			}
			break
		}
	}

	return h.showCustomers(c)
}

// validateSession checks if the user is logged in and has a valid session
func (h *ShopHandler) validateSession(c telebot.Context) error {
	if !h.authService.ValidateSession(c.Sender().ID) {
		return fmt.Errorf("session expired")
	}
	return nil
}

// HandleCallback handles all callback queries
func (h *ShopHandler) HandleCallback(c telebot.Context) error {
	data := c.Callback().Data
	log.Printf("Shop callback received: %s", data)

	// Always respond to prevent the loading spinner
	defer c.Respond()

	// Check if this is a payment callback
	if strings.HasPrefix(data, "pay:") {
		parts := strings.Split(data, ":")
		if len(parts) >= 2 && parts[1] == "mpesa" {
			log.Printf("Handling MPesa payment callback")
			// Validate session first
			if err := h.validateSession(c); err != nil {
				return h.handleSessionError(c, err)
			}

			session := h.sessions.GetSession(c.Sender().ID)
			if session == nil || !session.IsAuthenticated() {
				return c.Edit("Please login first to make a payment.")
			}

			customer := session.GetActiveCustomer()
			if customer == nil || len(customer.Cart) == 0 {
				return c.Edit("Your cart is empty. Please add items before checkout.")
			}

			// Update session state for phone number input
			session.CurrentMenu = "payment_mpesa"
			session.AwaitingSearch = true // Use this to indicate waiting for phone number
			if err := h.sessions.SaveSession(session); err != nil {
				log.Printf("Error saving session for MPesa payment: %v", err)
				return c.Edit("Failed to process payment request. Please try again.")
			}

			return c.Edit(
				"*"+escapeMarkdownV2("Enter M-Pesa Phone Number")+"*\n\n"+
					escapeMarkdownV2("Please enter the phone number to receive M-Pesa payment prompt")+
					"\nFormat: 254XXXXXXXXX",
				&telebot.ReplyMarkup{InlineKeyboard: [][]telebot.InlineButton{
					{{Text: "🔙 Cancel Payment", Data: "checkout:cancel"}},
				}},
				telebot.ModeMarkdownV2,
			)
		}
		return nil // Let other handlers process different payment types
	}

	// Validate session first
	if err := h.validateSession(c); err != nil {
		return h.handleSessionError(c, err)
	}

	// Check warehouse before processing any shop actions
	warehouse, err := h.shopService.GetUserWarehouse(c.Sender().ID)
	if err != nil || warehouse == "" {
		return c.Edit(
			"Please select a warehouse first using /warehouse command",
			&telebot.ReplyMarkup{InlineKeyboard: [][]telebot.InlineButton{
				{{Text: "🏭 Select Warehouse", Data: "warehouse"}},
			}},
		)
	}

	parts := strings.Split(data, ":")
	log.Printf("Callback parts: %v", parts)

	if len(parts) < 1 {
		log.Printf("Invalid callback data format: %s", data)
		return nil
	}

	switch parts[0] {
	case "view_cart": // Add this case to handle direct cart viewing
		return h.showCart(c)

	case "cancel":
		return h.showCart(c)

	case "customers":
		if len(parts) == 1 {
			return h.showCustomers(c)
		}

		switch parts[1] {
		case "switch":
			if len(parts) < 3 {
				return c.Respond(&telebot.CallbackResponse{
					Text:      "Invalid customer ID",
					ShowAlert: true,
				})
			}
			customerID := parts[2]
			session := h.sessions.GetSession(c.Sender().ID)
			if _, exists := session.Customers[customerID]; exists {
				session.ActiveCustomerID = customerID
				if err := h.sessions.SaveSession(session); err != nil {
					log.Printf("Error saving session after switching customer: %v", err)
					return c.Respond(&telebot.CallbackResponse{
						Text:      "Failed to switch customer",
						ShowAlert: true,
					})
				}
				// Respond to confirm the switch
				if err := c.Respond(&telebot.CallbackResponse{
					Text: fmt.Sprintf("Switched to customer: %s", customerID),
				}); err != nil {
					log.Printf("Error responding to switch: %v", err)
				}
				return h.showCustomers(c)
			}
			return c.Respond(&telebot.CallbackResponse{
				Text:      "Customer not found",
				ShowAlert: true,
			})

		case "new":
			session := h.sessions.GetSession(c.Sender().ID)
			// Generate a unique customer ID using timestamp
			customerID := fmt.Sprintf("customer_%d", time.Now().Unix())
			session.Customers[customerID] = &services.CustomerSession{
				CustomerID: customerID,
				Cart:       make(map[string]*services.CartItem),
				CreatedAt:  time.Now(),
				LastActive: time.Now(),
			}
			session.ActiveCustomerID = customerID
			if err := h.sessions.SaveSession(session); err != nil {
				log.Printf("Error saving session after creating customer: %v", err)
				return c.Respond(&telebot.CallbackResponse{
					Text:      "Failed to create customer",
					ShowAlert: true,
				})
			}
			// Respond to confirm the creation
			if err := c.Respond(&telebot.CallbackResponse{
				Text: fmt.Sprintf("Created new customer: %s", customerID),
			}); err != nil {
				log.Printf("Error responding to new customer: %v", err)
			}
			return h.showCustomers(c)
		}
		return c.Respond()

	case "shop":
		return h.showCategories(c)

	case "cat":
		log.Printf("Category callback received with parts: %v", parts)
		if len(parts) > 1 {
			return h.showProducts(c, parts[1])
		}
		log.Printf("Missing category ID in callback")
		return nil

	case "product":
		if len(parts) > 1 {
			return h.showProductDetails(c, parts[1])
		}

	case "qty":
		return h.handleQuantity(c)

	case "cart":
		if len(parts) == 1 {
			// Ensure we have a session and active customer before showing cart
			session := h.sessions.GetSession(c.Sender().ID)
			if session.ActiveCustomerID == "" {
				return c.Edit(
					"Please select a customer first\\.",
					&telebot.ReplyMarkup{InlineKeyboard: [][]telebot.InlineButton{
						{{Text: "👥 Select Customer", Data: "customers"}},
					}},
					telebot.ModeMarkdownV2,
				)
			}
			return h.showCart(c)
		} else if len(parts) >= 2 {
			if len(parts) == 3 {
				return h.handleCartAction(c, parts[1], parts[2])
			} else {
				return h.handleCartAction(c, parts[1], "")
			}
		}

	case "page":
		if len(parts) > 2 {
			return h.showProducts(c, parts[1])
		}

	case "add":
		if len(parts) > 1 {
			if err := h.handleAddToCart(c, parts[1]); err == nil {
				return h.showCart(c)
			}
		}

	case "switch":
		customerID := parts[1]
		session := h.sessions.GetSession(c.Sender().ID)
		if _, exists := session.Customers[customerID]; exists {
			session.ActiveCustomerID = customerID
			if err := h.sessions.SaveSession(session); err != nil {
				log.Printf("Error saving session after switching customer: %v", err)
				return c.Respond(&telebot.CallbackResponse{
					Text:      "Failed to switch customer",
					ShowAlert: true,
				})
			}
			return h.showCustomers(c)
		}
		return c.Respond()

	case "new_customer":
		session := h.sessions.GetSession(c.Sender().ID)
		// Generate a unique customer ID using timestamp
		// make the id more simple to remember
		customerID := fmt.Sprintf("customer_%d", time.Now().Unix())
		session.Customers[customerID] = &services.CustomerSession{
			CustomerID: customerID,
			Cart:       make(map[string]*services.CartItem),
			CreatedAt:  time.Now(),
			LastActive: time.Now(),
		}
		session.ActiveCustomerID = customerID
		if err := h.sessions.SaveSession(session); err != nil {
			log.Printf("Error saving session after creating customer: %v", err)
			return c.Respond(&telebot.CallbackResponse{
				Text:      "Failed to create customer",
				ShowAlert: true,
			})
		}
		return h.showCustomers(c)
	case "categories":
		return h.showCategories(c)

	case "search":
		// Set search state
		session := h.sessions.GetSession(c.Sender().ID)
		session.AwaitingSearch = true
		if err := h.sessions.SaveSession(session); err != nil {
			log.Printf("Error saving session before search: %v", err)
		}

		return c.Edit(
			"Enter your search term:",
			&telebot.ReplyMarkup{InlineKeyboard: [][]telebot.InlineButton{
				{{Text: "↩️ Back to Categories", Data: "shop"}},
			}},
			telebot.ModeMarkdownV2,
		)

	}

	return c.Respond(&telebot.CallbackResponse{
		Text:      "Invalid action. Please try again or use /shop to start over.",
		ShowAlert: true,
	})
}

// Constants for pagination
const (
	productsPerPage   = 5 // Number of products to show per page
	categoriesPerPage = 6 // Number of categories to show per page
)

// showCategories displays available product categories
func (h *ShopHandler) showCategories(c telebot.Context) error {
	// Validate session first
	if err := h.validateSession(c); err != nil {
		return err
	}

	// Check warehouse first
	warehouse, err := h.shopService.GetUserWarehouse(c.Sender().ID)
	if err != nil || warehouse == "" {
		return c.Send(
			"Please select a warehouse first using /warehouse command",
			&telebot.ReplyMarkup{InlineKeyboard: [][]telebot.InlineButton{
				{{Text: "🏭 Select Warehouse", Data: "warehouse"}},
			}},
		)
	}

	categories, err := h.shopService.GetCategories(warehouse)
	if err != nil {
		log.Printf("Error fetching categories: %v", err)
		return c.Send("Failed to fetch categories. Please try again.")
	}

	if len(categories) == 0 {
		msg := "No categories available\\."
		if c.Callback() != nil {
			return c.Edit(
				msg,
				&telebot.ReplyMarkup{InlineKeyboard: [][]telebot.InlineButton{
					{{Text: "🔄 Refresh", Data: "shop:keep"}},
					{{Text: "🏭 Change Warehouse", Data: "warehouse"}},
				}},
				telebot.ModeMarkdownV2,
			)
		}
		return c.Send(
			msg,
			&telebot.ReplyMarkup{InlineKeyboard: [][]telebot.InlineButton{
				{{Text: "🔄 Refresh", Data: "shop:keep"}},
				{{Text: "🏭 Change Warehouse", Data: "warehouse"}},
			}},
			telebot.ModeMarkdownV2,
		)
	}

	// Get current page from callback data
	currentPage := 0
	if c.Callback() != nil {
		parts := strings.Split(c.Callback().Data, ":")
		if len(parts) > 1 && parts[0] == "page" {
			currentPage, _ = strconv.Atoi(parts[1])
		}
	}

	// Calculate pagination
	totalPages := (len(categories) + categoriesPerPage - 1) / categoriesPerPage
	startIdx := currentPage * categoriesPerPage
	endIdx := min(startIdx+categoriesPerPage, len(categories))

	// Build message
	var msg strings.Builder
	msg.WriteString(fmt.Sprintf("🏭 *Warehouse:* `%s`\n\n", warehouse))
	msg.WriteString("*Available Categories:*\n")
	msg.WriteString("```\n") // Start monospace block

	// Find the longest category name for padding
	maxNameLen := 0
	for _, cat := range categories[startIdx:endIdx] {
		if len(cat.Name) > maxNameLen {
			maxNameLen = len(cat.Name)
		}
	}

	// Add header
	headerFormat := fmt.Sprintf("%%-%ds  %%8s\n", maxNameLen)
	msg.WriteString(fmt.Sprintf(headerFormat, "Category", "Items"))
	msg.WriteString(strings.Repeat("-", maxNameLen+10) + "\n")

	// Add categories with product counts
	itemFormat := fmt.Sprintf("%%-%ds  %%8d\n", maxNameLen)
	for _, cat := range categories[startIdx:endIdx] {
		productCount := len(h.shopService.GetProductsByCategory(cat.ID, warehouse))
		msg.WriteString(fmt.Sprintf(itemFormat, cat.Name, productCount))
	}
	msg.WriteString("```\n") // End monospace block

	// Create buttons
	var buttons [][]telebot.InlineButton

	// Add category buttons (2 per row)
	var row []telebot.InlineButton
	for _, category := range categories[startIdx:endIdx] {
		// Create a URL-safe ID from the category name
		safeID := strings.ReplaceAll(category.ID, " ", "_")
		log.Printf("Creating button for category: %s with safe ID: %s", category.Name, safeID)
		button := telebot.InlineButton{
			Text: fmt.Sprintf("📁 %s", escapeMarkdownV2(category.Name)),
			Data: fmt.Sprintf("cat:%s", safeID),
		}
		row = append(row, button)
		if len(row) == 2 {
			buttons = append(buttons, row)
			row = nil
		}
	}
	if len(row) > 0 {
		buttons = append(buttons, row)
	}

	// Add navigation buttons if needed
	if totalPages > 1 {
		var navRow []telebot.InlineButton
		if currentPage > 0 {
			navRow = append(navRow, telebot.InlineButton{
				Text: "⬅️ Previous",
				Data: fmt.Sprintf("page:%d", currentPage-1),
			})
		}
		navRow = append(navRow, telebot.InlineButton{
			Text: fmt.Sprintf("📄 %d/%d", currentPage+1, totalPages),
			Data: "noop",
		})
		if currentPage < totalPages-1 {
			navRow = append(navRow, telebot.InlineButton{
				Text: "Next ��️",
				Data: fmt.Sprintf("page:%d", currentPage+1),
			})
		}
		buttons = append(buttons, navRow)
	}

	// Add utility buttons row
	var utilityRow []telebot.InlineButton

	// Get session to check cart
	session := h.sessions.GetSession(c.Sender().ID)
	customer := session.GetActiveCustomer()

	// Add customer button showing active customer
	if customer != nil {
		utilityRow = append(utilityRow, telebot.InlineButton{
			Text: fmt.Sprintf("👤 %s", escapeMarkdownV2(customer.CustomerID)),
			Data: "customers",
		})
	}

	// Add cart button with item count if cart has items
	if customer != nil {
		if len(customer.Cart) > 0 {
			total := 0
			for _, item := range customer.Cart {
				total += item.Quantity
			}
			utilityRow = append(utilityRow, telebot.InlineButton{
				Text: fmt.Sprintf("🛒 Cart (%d)", total),
				Data: "view_cart",
			})
		} else {
			utilityRow = append(utilityRow, telebot.InlineButton{
				Text: "🛒 Cart",
				Data: "view_cart",
			})
		}
	}

	buttons = append(buttons, utilityRow)

	// Add final action row
	buttons = append(buttons, []telebot.InlineButton{
		{Text: "🔍 Search", Data: "search"},
		{Text: "🏭 Change Warehouse", Data: "warehouse"},
	})

	if c.Callback() != nil {
		return c.Edit(
			msg.String(),
			&telebot.ReplyMarkup{InlineKeyboard: buttons},
			telebot.ModeMarkdownV2,
		)
	}

	return c.Send(
		msg.String(),
		&telebot.ReplyMarkup{InlineKeyboard: buttons},
		telebot.ModeMarkdownV2,
	)
}

// showCustomers displays the list of customers and management options
func (h *ShopHandler) showCustomers(c telebot.Context) error {
	session := h.sessions.GetSession(c.Sender().ID)

	// Safety check - ensure we have customers
	if session.Customers == nil {
		session.Customers = make(map[string]*services.CustomerSession)
	}
	if len(session.Customers) == 0 {
		defaultID := "default"
		session.Customers[defaultID] = &services.CustomerSession{
			CustomerID: defaultID,
			Cart:       make(map[string]*services.CartItem),
			CreatedAt:  time.Now(),
			LastActive: time.Now(),
		}
		session.ActiveCustomerID = defaultID
		if err := h.sessions.SaveSession(session); err != nil {
			log.Printf("Error saving session after creating default customer: %v", err)
		}
	}

	var msg strings.Builder
	msg.WriteString("👥 *Customer Management*\n\n")

	// Show active customer
	activeCustomer := session.Customers[session.ActiveCustomerID]
	if activeCustomer == nil {
		// If active customer is invalid, set first available customer as active
		for id, customer := range session.Customers {
			session.ActiveCustomerID = id
			activeCustomer = customer
			if err := h.sessions.SaveSession(session); err != nil {
				log.Printf("Error saving session after fixing active customer: %v", err)
			}
			break
		}
	}

	msg.WriteString(fmt.Sprintf("Active Customer: `%s`\n\n", activeCustomer.CustomerID))

	// List all customers
	msg.WriteString("*Available Customers:*\n")
	for id, cust := range session.Customers {
		activeMarker := " "
		if id == session.ActiveCustomerID {
			activeMarker = "✓"
		}
		cartCount := len(cust.Cart)
		msg.WriteString(fmt.Sprintf("%s `%s` \\(%d items\\)\n",
			activeMarker,
			id,
			cartCount,
		))
	}

	// Create buttons
	var buttons [][]telebot.InlineButton

	// Add switch buttons for each customer
	for id := range session.Customers {
		if id != session.ActiveCustomerID {
			buttons = append(buttons, []telebot.InlineButton{
				{Text: fmt.Sprintf("🔄 Switch to %s", id), Data: fmt.Sprintf("customers:switch:%s", id)},
			})
		}
	}

	// Add new customer button
	buttons = append(buttons, []telebot.InlineButton{
		{Text: "➕ New Customer", Data: "customers:new"},
	})

	// Add back to shop button
	buttons = append(buttons, []telebot.InlineButton{
		{Text: "🏪 Back to Shop", Data: "shop"},
	})

	if c.Callback() != nil {
		return c.Edit(
			msg.String(),
			&telebot.ReplyMarkup{InlineKeyboard: buttons},
			telebot.ModeMarkdownV2,
		)
	}

	return c.Send(
		msg.String(),
		&telebot.ReplyMarkup{InlineKeyboard: buttons},
		telebot.ModeMarkdownV2,
	)
}

// showProducts displays products in a category
func (h *ShopHandler) showProducts(c telebot.Context, categoryID string) error {
	// Convert URL-safe ID back to original format
	originalID := strings.ReplaceAll(categoryID, "_", " ")
	log.Printf("Showing products for category. Safe ID: %s, Original ID: %s", categoryID, originalID)

	if err := h.validateSession(c); err != nil {
		log.Printf("Session validation failed: %v", err)
		return err
	}

	warehouse, err := h.shopService.GetUserWarehouse(c.Sender().ID)
	if err != nil {
		log.Printf("Error getting user warehouse: %v", err)
		return err
	}

	products := h.shopService.GetProductsByCategory(originalID, warehouse)
	log.Printf("Found %d products in category %s", len(products), originalID)

	if len(products) == 0 {
		return c.Send(
			fmt.Sprintf("No products available in this category for warehouse: %s", warehouse),
			&telebot.ReplyMarkup{InlineKeyboard: [][]telebot.InlineButton{
				{{Text: "↩️ Back to Categories", Data: "shop"}},
			}},
			telebot.ModeMarkdownV2,
		)
	}

	// Get current page from callback data
	currentPage := 0
	if c.Callback() != nil {
		parts := strings.Split(c.Callback().Data, ":")
		if len(parts) > 2 && parts[0] == "page" {
			currentPage, _ = strconv.Atoi(parts[2])
		}
	}

	// Calculate pagination
	productsPerPage := 5 // Define this constant at package level
	totalPages := (len(products) + productsPerPage - 1) / productsPerPage
	startIdx := currentPage * productsPerPage
	endIdx := min(startIdx+productsPerPage, len(products))

	var msg strings.Builder
	msg.WriteString("*Products in Category*\n\n")
	msg.WriteString("```\n") // Start monospace block

	// Find the longest product name for padding
	maxNameLen := 0
	for _, prod := range products[startIdx:endIdx] {
		if len(prod.Name) > maxNameLen {
			maxNameLen = len(prod.Name)
		}
	}

	// Add header with proper alignment
	headerFormat := fmt.Sprintf("%%-%ds  %%8s  %%8s\n", maxNameLen)
	msg.WriteString(fmt.Sprintf(headerFormat, "Product", "Price", "Stock"))
	msg.WriteString(strings.Repeat("-", maxNameLen+20) + "\n")

	// Add products with proper alignment
	itemFormat := fmt.Sprintf("%%-%ds  %%8.2f  %%8d\n", maxNameLen)
	for _, product := range products[startIdx:endIdx] {
		msg.WriteString(fmt.Sprintf(itemFormat,
			product.Name,
			product.Price,
			product.StockQty,
		))
	}
	msg.WriteString("```\n") // End monospace block
	msg.WriteString("\nSelect a product to view details:")

	// Create buttons
	var buttons [][]telebot.InlineButton
	var row []telebot.InlineButton

	// Add product buttons (2 per row)
	for _, product := range products[startIdx:endIdx] {
		button := telebot.InlineButton{
			Text: fmt.Sprintf("📦 %s - %.2f KES",
				escapeMarkdownV2(product.Name),
				product.Price),
			Data: fmt.Sprintf("product:%s", product.ID),
		}
		row = append(row, button)
		if len(row) == 2 {
			buttons = append(buttons, row)
			row = nil
		}
	}
	if len(row) > 0 {
		buttons = append(buttons, row)
	}

	// Add navigation buttons
	var navButtons []telebot.InlineButton
	if currentPage > 0 {
		navButtons = append(navButtons, telebot.InlineButton{
			Text: "◀️ Previous",
			Data: fmt.Sprintf("page:%s:%d", categoryID, currentPage-1),
		})
	}
	if currentPage < totalPages-1 {
		navButtons = append(navButtons, telebot.InlineButton{
			Text: "Next ��️",
			Data: fmt.Sprintf("page:%s:%d", categoryID, currentPage+1),
		})
	}
	if len(navButtons) > 0 {
		buttons = append(buttons, navButtons)
	}

	// Add back button
	buttons = append(buttons, []telebot.InlineButton{
		{Text: "↩️ Back to Categories", Data: "shop"},
	})

	markup := &telebot.ReplyMarkup{
		InlineKeyboard: buttons,
	}

	if c.Callback() != nil {
		return c.Edit(msg.String(), markup, telebot.ModeMarkdownV2)
	}
	return c.Send(msg.String(), markup, telebot.ModeMarkdownV2)
}

// showProductDetails shows product details and quantity selector
func (h *ShopHandler) showProductDetails(c telebot.Context, productID string) error {
	// Validate session
	if err := h.validateSession(c); err != nil {
		return err
	}
	// Get user's warehouse first
	warehouse, err := h.shopService.GetUserWarehouse(c.Sender().ID)
	if err != nil {
		log.Printf("Error getting user warehouse: %v", err)
		return err
	}

	if warehouse == "" {
		return c.Send(
			"Please select a warehouse first using /warehouse command",
			&telebot.ReplyMarkup{InlineKeyboard: [][]telebot.InlineButton{
				{{Text: "🏭 Select Warehouse", Data: "warehouse"}},
			}},
		)
	}
	product := h.shopService.GetProduct(productID, warehouse)
	if product == nil {
		return fmt.Errorf("product not found")
	}

	session := h.sessions.GetSession(c.Sender().ID)

	// Get current quantity from session or cart
	qty := 1
	if v, ok := session.SelectedItems["qty"]; ok {
		switch val := v.(type) {
		case int:
			qty = val
		case float64:
			qty = int(val)
		}
	}

	// Show current quantity if item is in cart
	customer := session.GetActiveCustomer()
	if customer != nil {
		if cartItem, exists := customer.Cart[productID]; exists {
			qty = cartItem.Quantity
		}
	}

	var msg strings.Builder
	msg.WriteString(fmt.Sprintf("*%s*\n\n", escapeMarkdownV2(product.Name)))
	msg.WriteString("```\n") // Start monospace block
	msg.WriteString(fmt.Sprintf("Price:     %.2f\n", product.Price))
	msg.WriteString(fmt.Sprintf("Available: %d\n", product.StockQty))
	if customer != nil {
		if cartItem, exists := customer.Cart[productID]; exists {
			msg.WriteString(fmt.Sprintf("\nQuantity in cart: %d", cartItem.Quantity))
		}
	}
	msg.WriteString("```\n") // End monospace block

	// Create buttons
	var buttons [][]telebot.InlineButton

	// Add quantity selector buttons
	var qtyRow []telebot.InlineButton
	if qty > 1 {
		qtyRow = append(qtyRow, telebot.InlineButton{
			Text: "➖",
			Data: fmt.Sprintf("qty:%s:dec", productID),
		})
	}
	qtyRow = append(qtyRow, telebot.InlineButton{
		Text: fmt.Sprintf("Qty: %d", qty),
		Data: "noop",
	})
	if qty < product.StockQty {
		qtyRow = append(qtyRow, telebot.InlineButton{
			Text: "➕",
			Data: fmt.Sprintf("qty:%s:inc", productID),
		})
	}
	buttons = append(buttons, qtyRow)

	// Add action buttons
	var actionRow []telebot.InlineButton
	actionRow = append(actionRow, telebot.InlineButton{
		Text: "⬅️ Back",
		Data: fmt.Sprintf("cat:%s", product.CategoryID),
	})

	// Show different button based on whether item is in cart
	if customer != nil {
		if _, exists := customer.Cart[productID]; exists {
			actionRow = append(actionRow, telebot.InlineButton{
				Text: "🛒 Update Cart",
				Data: fmt.Sprintf("add:%s", productID),
			})
		} else {
			actionRow = append(actionRow, telebot.InlineButton{
				Text: "🛒 Add to Cart",
				Data: fmt.Sprintf("add:%s", productID),
			})
		}
	}
	buttons = append(buttons, actionRow)

	// Add view cart button if cart has items
	if customer != nil && len(customer.Cart) > 0 {
		buttons = append(buttons, []telebot.InlineButton{
			{Text: "📝 View Cart", Data: "view_cart"},
		})
	}

	if c.Callback() != nil {
		return c.Edit(
			msg.String(),
			&telebot.ReplyMarkup{InlineKeyboard: buttons},
			telebot.ModeMarkdownV2,
		)
	}
	return c.Send(
		msg.String(),
		&telebot.ReplyMarkup{InlineKeyboard: buttons},
		telebot.ModeMarkdownV2,
	)
}

// handleQuantity handles quantity adjustment callbacks
func (h *ShopHandler) handleQuantity(c telebot.Context) error {
	// Validate session
	if err := h.validateSession(c); err != nil {
		return err
	}
	// Get user's warehouse first
	warehouse, err := h.shopService.GetUserWarehouse(c.Sender().ID)
	if err != nil {
		log.Printf("Error getting user warehouse: %v", err)
		return err
	}

	if warehouse == "" {
		return c.Send(
			"Please select a warehouse first using /warehouse command",
			&telebot.ReplyMarkup{InlineKeyboard: [][]telebot.InlineButton{
				{{Text: "🏭 Select Warehouse", Data: "warehouse"}},
			}},
		)
	}

	// Parse callback data
	parts := strings.Split(c.Callback().Data, ":")
	if len(parts) != 3 {
		return c.Respond(&telebot.CallbackResponse{
			Text:      "Invalid quantity adjustment data",
			ShowAlert: true,
		})
	}

	productID := parts[1]
	action := parts[2]

	// Get product to validate
	product := h.shopService.GetProduct(productID, warehouse)
	if product == nil {
		return c.Respond(&telebot.CallbackResponse{
			Text:      "Product not found",
			ShowAlert: true,
		})
	}

	session := h.sessions.GetSession(c.Sender().ID)

	// Initialize session.SelectedItems if nil
	if session.SelectedItems == nil {
		session.SelectedItems = make(map[string]interface{})
	}

	// Get current quantity
	var qty int = 1 // Default to 1 if not set
	if v, ok := session.SelectedItems["qty"]; ok {
		switch val := v.(type) {
		case int:
			qty = val
		case float64:
			qty = int(val)
		}
	}

	// Show current quantity if item is in cart
	customer := session.GetActiveCustomer()
	if customer != nil {
		if cartItem, exists := customer.Cart[productID]; exists {
			qty = cartItem.Quantity
		}
	}

	// Calculate new quantity
	oldQty := qty
	switch action {
	case "inc":
		if qty < product.StockQty {
			qty++
		}
	case "dec":
		if qty > 1 {
			qty--
		}
	default:
		return c.Respond(&telebot.CallbackResponse{
			Text:      "Invalid quantity adjustment action",
			ShowAlert: true,
		})
	}

	// Only update if quantity changed
	if oldQty != qty {
		if customer != nil {
			if _, exists := customer.Cart[productID]; exists {
				// Update cart directly
				customer.Cart[productID].Quantity = qty
			} else {
				// Update selected items for new product
				session.SelectedItems["product"] = productID
				session.SelectedItems["qty"] = qty
			}

			// Save session
			if err := h.sessions.SaveSession(session); err != nil {
				log.Printf("Error saving session: %v", err)
				return c.Respond(&telebot.CallbackResponse{
					Text:      "Failed to update quantity",
					ShowAlert: true,
				})
			}

			// Show updated view
			if _, exists := customer.Cart[productID]; exists {
				return h.showCart(c)
			}
			return h.showProductDetails(c, productID)
		}
	}

	return c.Respond()
}

// handleAddToCart handles adding or updating items in cart
func (h *ShopHandler) handleAddToCart(c telebot.Context, productID string) error {
	// Validate session
	if err := h.validateSession(c); err != nil {
		return err
	}

	session := h.sessions.GetSession(c.Sender().ID)
	customer := session.GetActiveCustomer()
	if customer == nil {
		return c.Send(
			"❌ No active customer\\. Create or select a customer first:",
			&telebot.ReplyMarkup{InlineKeyboard: [][]telebot.InlineButton{{
				{Text: "👥 Manage Customers", Data: "customers"},
			}}},
			telebot.ModeMarkdownV2,
		)
	}

	// Get quantity from session
	qty := 1
	if v, ok := session.SelectedItems["qty"]; ok {
		switch val := v.(type) {
		case int:
			qty = val
		case float64:
			qty = int(val)
		}
	}
	// Get user's warehouse first
	warehouse, err := h.shopService.GetUserWarehouse(c.Sender().ID)
	if err != nil {
		log.Printf("Error getting user warehouse: %v", err)
		return err
	}

	if warehouse == "" {
		return c.Send(
			"Please select a warehouse first using /warehouse command",
			&telebot.ReplyMarkup{InlineKeyboard: [][]telebot.InlineButton{
				{{Text: "🏭 Select Warehouse", Data: "warehouse"}},
			}},
		)
	}

	// Get product to validate
	product := h.shopService.GetProduct(productID, warehouse)
	if product == nil {
		return c.Respond(&telebot.CallbackResponse{
			Text:      "Product not found",
			ShowAlert: true,
		})
	}

	// Validate quantity against stock
	if qty > product.StockQty {
		return c.Respond(&telebot.CallbackResponse{
			Text:      fmt.Sprintf("Only %d items available in stock", product.StockQty),
			ShowAlert: true,
		})
	}

	// Initialize cart if needed
	if customer.Cart == nil {
		customer.Cart = make(map[string]*services.CartItem)
	}

	// Check if quantity is different from what's in cart
	oldCartItem, exists := customer.Cart[productID]
	if exists && oldCartItem.Quantity == qty {
		return c.Respond(&telebot.CallbackResponse{
			Text: "Cart already has this quantity",
		})
	}

	// Update cart
	customer.Cart[productID] = &services.CartItem{
		ProductID: productID,
		Quantity:  qty,
		Warehouse: warehouse,
		Price:     product.Price, // Add the price from the product
	}

	// Clear selected items after adding to cart
	session.SelectedItems = make(map[string]interface{})

	// Save session
	if err := h.sessions.SaveSession(session); err != nil {
		log.Printf("Error saving session after adding to cart: %v", err)
		return c.Respond(&telebot.CallbackResponse{
			Text:      "Failed to update cart",
			ShowAlert: true,
		})
	}

	// Show success message and update the cart view
	if err := c.Respond(&telebot.CallbackResponse{
		Text: fmt.Sprintf("Added %d %s to cart", qty, product.Name),
	}); err != nil {
		log.Printf("Error responding to callback: %v", err)
	}

	return h.showCart(c)
}

// showCart displays the current shopping cart
func (h *ShopHandler) showCart(c telebot.Context) error {
	// Validate session
	if err := h.validateSession(c); err != nil {
		return err
	}

	session := h.sessions.GetSession(c.Sender().ID)
	customer := session.GetActiveCustomer()
	if customer == nil || len(customer.Cart) == 0 {
		return c.Edit(
			"Your cart is empty\\. Use /shop to browse products\\.",
			&telebot.ReplyMarkup{InlineKeyboard: [][]telebot.InlineButton{
				{{Text: "🏪 Continue Shopping", Data: "shop:keep"}},
			}},
			telebot.ModeMarkdownV2,
		)
	}

	var msg strings.Builder
	msg.WriteString("*Shopping Cart* 🛒\n\n")
	msg.WriteString("```\n") // Start monospace block

	// Find the longest product name for padding
	maxNameLen := 0
	var total float64
	for productID, cartItem := range customer.Cart {
		product := h.shopService.GetProduct(productID, cartItem.Warehouse)
		if product != nil && len(product.Name) > maxNameLen {
			maxNameLen = len(product.Name)
		}
	}

	// Add header
	headerFormat := fmt.Sprintf("%%-%ds  %%8s  %%8s  %%10s\n", maxNameLen)
	msg.WriteString(fmt.Sprintf(headerFormat, "Product", "Price", "Qty", "Subtotal"))
	msg.WriteString(strings.Repeat("-", maxNameLen+30) + "\n")

	// Add cart items
	itemFormat := fmt.Sprintf("%%-%ds  %%8.2f  %%8d  %%10.2f\n", maxNameLen)
	for productID, cartItem := range customer.Cart {
		product := h.shopService.GetProduct(productID, cartItem.Warehouse)
		if product == nil {
			continue
		}
		subtotal := float64(cartItem.Quantity) * product.Price
		total += subtotal
		msg.WriteString(fmt.Sprintf(itemFormat,
			product.Name,
			product.Price,
			cartItem.Quantity,
			subtotal,
		))
	}

	// Add total
	msg.WriteString(strings.Repeat("-", maxNameLen+30) + "\n")
	msg.WriteString(fmt.Sprintf(fmt.Sprintf("%%%ds  %%10.2f\n", maxNameLen+19),
		"Total:",
		total,
	))
	msg.WriteString("```\n") // End monospace block

	// Create buttons for cart actions
	var buttons [][]telebot.InlineButton

	// Add item modification buttons
	for productID, cartItem := range customer.Cart {
		product := h.shopService.GetProduct(productID, cartItem.Warehouse)
		if product == nil {
			continue
		}

		// Add product name and current quantity
		buttons = append(buttons, []telebot.InlineButton{
			{Text: fmt.Sprintf("%s (%d)", product.Name, cartItem.Quantity), Data: fmt.Sprintf("product:%s", productID)},
		})

		// Add quantity controls and remove button
		var controlRow []telebot.InlineButton
		if cartItem.Quantity > 1 {
			controlRow = append(controlRow, telebot.InlineButton{
				Text: "➖",
				Data: fmt.Sprintf("qty:%s:dec", productID),
			})
		}
		if cartItem.Quantity < product.StockQty {
			controlRow = append(controlRow, telebot.InlineButton{
				Text: "➕",
				Data: fmt.Sprintf("qty:%s:inc", productID),
			})
		}
		controlRow = append(controlRow, telebot.InlineButton{
			Text: "❌",
			Data: fmt.Sprintf("cart:remove:%s", productID),
		})
		buttons = append(buttons, controlRow)
	}

	// Add cart action buttons
	buttons = append(buttons, []telebot.InlineButton{
		{Text: "🗑️ Clear Cart", Data: "cart:clear"},
		{Text: "✅ Checkout", Data: "cart:checkout"},
	})

	// Add continue shopping button
	buttons = append(buttons, []telebot.InlineButton{
		{Text: "🏪 Continue Shopping", Data: "shop:keep"},
	})

	if c.Callback() != nil {
		return c.Edit(
			msg.String(),
			&telebot.ReplyMarkup{InlineKeyboard: buttons},
			telebot.ModeMarkdownV2,
		)
	}
	return c.Send(
		msg.String(),
		&telebot.ReplyMarkup{InlineKeyboard: buttons},
		telebot.ModeMarkdownV2,
	)
}

// handleCartAction handles various cart actions (remove, clear, checkout)
func (h *ShopHandler) handleCartAction(c telebot.Context, action string, productID string) error {
	// Validate session
	if err := h.validateSession(c); err != nil {
		return err
	}

	session := h.sessions.GetSession(c.Sender().ID)
	customer := session.GetActiveCustomer()
	if customer == nil {
		return c.Respond(&telebot.CallbackResponse{
			Text:      "No active customer",
			ShowAlert: true,
		})
	}

	switch action {
	case "remove":
		if _, exists := customer.Cart[productID]; exists {
			delete(customer.Cart, productID)
			if err := h.sessions.SaveSession(session); err != nil {
				log.Printf("Error saving session after removing item: %v", err)
			}
			return h.showCart(c)
		}

	case "clear":
		customer.Cart = make(map[string]*services.CartItem)
		if err := h.sessions.SaveSession(session); err != nil {
			log.Printf("Error saving session after clearing cart: %v", err)
		}
		return c.Edit(
			"Your cart has been cleared\\.",
			&telebot.ReplyMarkup{InlineKeyboard: [][]telebot.InlineButton{
				{{Text: "🏪 Back to Shop", Data: "shop"}},
			}},
			telebot.ModeMarkdownV2,
		)

	case "checkout":
		// Process checkout
		if len(customer.Cart) == 0 {
			return c.Respond(&telebot.CallbackResponse{
				Text:      "Your cart is empty",
				ShowAlert: true,
			})
		}

		msg := &strings.Builder{}
		msg.WriteString("*Checkout Summary*\n\n")

		// Add cart details for checkout
		msg.WriteString("```\n") // Start monospace block

		// Find the longest product name for padding
		maxNameLen := 0
		var total float64
		for productID, cartItem := range customer.Cart {
			product := h.shopService.GetProduct(productID, cartItem.Warehouse)
			if product != nil && len(product.Name) > maxNameLen {
				maxNameLen = len(product.Name)
			}
		}

		// Add header
		headerFormat := fmt.Sprintf("%%-%ds  %%8s  %%8s  %%10s\n", maxNameLen)
		msg.WriteString(fmt.Sprintf(headerFormat, "Product", "Price", "Qty", "Subtotal"))
		msg.WriteString(strings.Repeat("-", maxNameLen+30) + "\n")

		// Add cart items
		itemFormat := fmt.Sprintf("%%-%ds  %%8.2f  %%8d  %%10.2f\n", maxNameLen)
		for productID, cartItem := range customer.Cart {
			product := h.shopService.GetProduct(productID, cartItem.Warehouse)
			if product == nil {
				continue
			}
			subtotal := float64(cartItem.Quantity) * product.Price
			total += subtotal
			msg.WriteString(fmt.Sprintf(itemFormat,
				product.Name,
				product.Price,
				cartItem.Quantity,
				subtotal,
			))
		}

		// Add total
		msg.WriteString(strings.Repeat("-", maxNameLen+30) + "\n")
		msg.WriteString(fmt.Sprintf(fmt.Sprintf("%%%ds  %%10.2f\n", maxNameLen+19),
			"Total:",
			total,
		))
		msg.WriteString("```\n") // End monospace block

		msg.WriteString("*Select Payment Method:*\n\n")
		msg.WriteString("• M\\-Pesa: Pay using mobile money\n")
		msg.WriteString("• On Account: Pay using credit \\(requires account\\)")

		buttons := [][]telebot.InlineButton{
			{{Text: "💳 M-Pesa", Data: "pay:mpesa"}},
			{{Text: "📊 On Account", Data: "pay:account"}},
			{{Text: "🔙 Back to Cart", Data: "cart"}},
		}

		return c.Edit(
			msg.String(),
			&telebot.ReplyMarkup{InlineKeyboard: buttons},
			telebot.ModeMarkdownV2,
		)
	}

	return c.Respond()
}

// Helper function to escape special characters for MarkdownV2
func escapeMarkdownV2(text string) string {
	specialChars := []string{"_", "*", "[", "]", "(", ")", "~", "`", ">", "#", "+", "-", "=", "|", "{", "}", ".", "!", ","}
	escaped := text
	for _, char := range specialChars {
		escaped = strings.ReplaceAll(escaped, char, "\\"+char)
	}
	return escaped
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func (h *ShopHandler) handleProductSearch(c telebot.Context, searchTerm string) error {
	// Validate session
	if err := h.validateSession(c); err != nil {
		return err
	}

	log.Printf("Searching products with term: %s", searchTerm)

	products := h.shopService.SearchProducts(searchTerm)
	if len(products) == 0 {
		return c.Send(
			fmt.Sprintf("No products found matching '%s'\\. Try another search term\\.", escapeMarkdownV2(searchTerm)),
			&telebot.ReplyMarkup{InlineKeyboard: [][]telebot.InlineButton{
				{{Text: "↩️ Back to Categories", Data: "shop"}},
			}},
			telebot.ModeMarkdownV2,
		)
	}

	var msg strings.Builder
	msg.WriteString(fmt.Sprintf("*Search Results for '%s'*\n\n", escapeMarkdownV2(searchTerm)))
	msg.WriteString("```\n") // Start monospace block

	// Find the longest product name for padding
	maxNameLen := 0
	for _, prod := range products {
		if len(prod.Name) > maxNameLen {
			maxNameLen = len(prod.Name)
		}
	}

	// Add header with proper alignment
	headerFormat := fmt.Sprintf("%%-%ds  %%8s  %%8s  %%s\n", maxNameLen)
	msg.WriteString(fmt.Sprintf(headerFormat, "Product", "Price", "Stock", "Category"))
	msg.WriteString(strings.Repeat("-", maxNameLen+30) + "\n")

	// Add products with proper alignment
	itemFormat := fmt.Sprintf("%%-%ds  %%8.2f  %%8d  %%s\n", maxNameLen)
	for _, prod := range products {
		msg.WriteString(fmt.Sprintf(itemFormat, prod.Name, prod.Price, prod.StockQty, prod.CategoryID))
	}
	msg.WriteString("```\n") // End monospace block

	// Create buttons for products
	var buttons [][]telebot.InlineButton
	for _, prod := range products {
		buttons = append(buttons, []telebot.InlineButton{{
			Text: fmt.Sprintf("%s - %.2f KES", prod.Name, prod.Price),
			Data: fmt.Sprintf("product:%s", prod.ID),
		}})
	}

	// Add navigation buttons
	buttons = append(buttons, []telebot.InlineButton{
		{Text: "↩️ Back to Categories", Data: "shop"},
		{Text: "🔍 New Search", Data: "search"},
	})

	return c.Send(
		msg.String(),
		&telebot.ReplyMarkup{InlineKeyboard: buttons},
		telebot.ModeMarkdownV2,
	)
}

// handleText processes text messages, including phone numbers for MPesa payments
func (h *ShopHandler) handleText(c telebot.Context) error {
	session := h.sessions.GetSession(c.Sender().ID)
	if session == nil {
		return nil // No active session
	}

	// Check if we're waiting for MPesa phone number
	if session.CurrentMenu == "payment_mpesa" && session.AwaitingSearch {
		phoneNumber := strings.TrimSpace(c.Text())
		log.Printf("Processing M-Pesa phone number: %s", phoneNumber)

		// Reset the awaiting state
		session.AwaitingSearch = false
		session.CurrentMenu = ""
		if err := h.sessions.SaveSession(session); err != nil {
			log.Printf("Error saving session: %v", err)
		}

		if !strings.HasPrefix(phoneNumber, "254") || len(phoneNumber) != 12 {
			return c.Reply("⚠️ Invalid phone number format. Please use format: 254XXXXXXXXX\n\nTry again or click /cancel to abort.")
		}

		customerSession := session.GetActiveCustomer()
		if customerSession == nil || len(customerSession.Cart) == 0 {
			return c.Reply("⚠️ No active cart found. Please start over.")
		}

		// Show processing message
		if err := c.Reply("Processing your M-Pesa payment request..."); err != nil {
			log.Printf("Error sending processing message: %v", err)
		}
		// Get payment service from application context or dependency injection
		// paymentService := h.paymentService
		log.Printf("Payment service: %v", h.paymentService)
 
		// Process the payment with the phone number

		// Show success message with instructions
		msg := &strings.Builder{}
		msg.WriteString("*Payment Initiated*\n\n")
		msg.WriteString("Please check your phone for the M-Pesa prompt\n")
		msg.WriteString("Enter PIN to complete payment")

		buttons := [][]telebot.InlineButton{
			{{Text: "🔄 Check Status", Data: "payment:status"}},
			{{Text: "❌ Cancel", Data: "payment:cancel"}},
		}

		return c.Reply(
			msg.String(),
			&telebot.ReplyMarkup{InlineKeyboard: buttons},
			telebot.ModeMarkdownV2,
		)
	}

	if session.AwaitingSearch {
		log.Printf("Handling search term: %s", c.Text())
		// Reset the awaiting state
		session.AwaitingSearch = false
		if err := h.sessions.SaveSession(session); err != nil {
			log.Printf("Error saving session: %v", err)
		}
		searchTerm := strings.TrimSpace(c.Text())
		return h.handleProductSearch(c, searchTerm)
	}

	// Handle other text messages or pass to other handlers
	return nil
}

// handleSessionError handles session validation errors consistently
func (h *ShopHandler) handleSessionError(c telebot.Context, err error) error {
	// Always respond to callback to prevent loading spinner
	if c.Callback() != nil {
		if respErr := c.Respond(); respErr != nil {
			log.Printf("Error responding to callback: %v", respErr)
		}
	}

	// Log the original error
	log.Printf("Session error for user %d: %v", c.Sender().ID, err)

	// Prepare login message with button, including error context
	var message string
	if err.Error() == "session expired" {
		message = "⚠️ Your session has expired\\. Please log in again using /login\\."
	} else {
		message = "⚠️ Session error\\. Please log in again using /login\\."
	}

	markup := &telebot.ReplyMarkup{
		InlineKeyboard: [][]telebot.InlineButton{
			{{Text: "🔑 Login", Data: "login"}},
		},
	}

	// If it's a callback, edit the message
	if c.Callback() != nil {
		return c.Edit(message, markup, telebot.ModeMarkdownV2)
	}

	// Otherwise send a new message
	return c.Send(message, markup, telebot.ModeMarkdownV2)
}
