package handlers

import (
	"fmt"
	"log"
	"strconv"
	"strings"

	"github.com/itunza/telegram-silcore/internal/bot/utils"
	"github.com/itunza/telegram-silcore/internal/services"
	"gopkg.in/telebot.v3"
)

// ShopHandler handles shop-related commands
type ShopHandler struct {
	shopService     *services.ShopService
	sessions        *services.SessionService
	cartHandler     *CartHandler     // Added CartHandler dependency
	productHandler  *ProductHandler  // Added ProductHandler dependency
	categoryHandler *CategoryHandler // Added CategoryHandler dependency
}

// NewShopHandler creates a new ShopHandler instance
func NewShopHandler(shopService *services.ShopService, sessions *services.SessionService, cartHandler *CartHandler, productHandler *ProductHandler, categoryHandler *CategoryHandler) *ShopHandler {
	return &ShopHandler{
		shopService:     shopService,
		sessions:        sessions,
		cartHandler:     cartHandler,     // Store CartHandler
		productHandler:  productHandler,  // Store ProductHandler
		categoryHandler: categoryHandler, // Store CategoryHandler
	}
}

// RegisterHandlers registers all shop-related handlers
func (h *ShopHandler) RegisterHandlers(bot *telebot.Bot) {
	bot.Handle("/shop", h.Handle)
	bot.Handle(telebot.OnCallback, h.HandleCallback)
}

// Handle handles the /shop command
func (h *ShopHandler) Handle(c telebot.Context) error {
	if h.categoryHandler != nil {
		return h.categoryHandler.showCategories(c)
	}
	log.Println("Error: categoryHandler not initialized in ShopHandler.Handle")
	return c.Send("Shop categories are currently unavailable.")
}

// Constants for pagination
const (
// productsPerPage   = 5 // Moved to product_handler.go
// categoriesPerPage = 6 // Moved to category_handler.go
)

// showProducts displays products in a category
func (h *ShopHandler) showProducts(c telebot.Context, categoryID string) error {
	products := h.shopService.GetProductsByCategory(categoryID)
	if len(products) == 0 {
		// Add back button when no products are available
		return c.Edit(
			"No products available in this category\\.",
			&telebot.ReplyMarkup{InlineKeyboard: [][]telebot.InlineButton{
				{{Text: "⬅️ Back to Categories", Data: "shop"}},
			}},
			telebot.ModeMarkdownV2,
		)
	}

	// Get current page from callback data
	currentPage := 0
	if c.Callback() != nil {
		parts := strings.Split(c.Callback().Data, ":")
		if len(parts) > 2 && parts[0] == "page" {
			currentPage, _ = strconv.Atoi(parts[2])
		}
	}

	// Calculate pagination
	totalPages := (len(products) + productsPerPage - 1) / productsPerPage
	startIdx := currentPage * productsPerPage
	endIdx := utils.Min(startIdx+productsPerPage, len(products))

	var msg strings.Builder
	msg.WriteString(fmt.Sprintf("*Products in Category*\n\n"))
	msg.WriteString("```\n") // Start monospace block

	// Find the longest product name for padding
	maxNameLen := 0
	for _, prod := range products[startIdx:endIdx] {
		if len(prod.Name) > maxNameLen {
			maxNameLen = len(prod.Name)
		}
	}

	// Add header
	headerFormat := fmt.Sprintf("%%-%ds  %%8s  %%8s\n", maxNameLen)
	msg.WriteString(fmt.Sprintf(headerFormat, "Product", "Price", "Stock"))
	msg.WriteString(strings.Repeat("-", maxNameLen+20) + "\n")

	// Add products
	itemFormat := fmt.Sprintf("%%-%ds  %%8.2f  %%8d\n", maxNameLen)
	for _, product := range products[startIdx:endIdx] {
		msg.WriteString(fmt.Sprintf(itemFormat,
			product.Name,
			product.Price,
			product.StockQty,
		))
	}
	msg.WriteString("```\n") // End monospace block
	msg.WriteString("\nSelect a product to view details:")

	// Create buttons
	var buttons [][]telebot.InlineButton
	var row []telebot.InlineButton

	// Add product buttons (2 per row)
	for _, product := range products[startIdx:endIdx] {
		button := telebot.InlineButton{
			Text: fmt.Sprintf("📦 %s", product.Name),
			Data: fmt.Sprintf("product:%s", product.ID),
		}
		row = append(row, button)
		if len(row) == 2 {
			buttons = append(buttons, row)
			row = nil
		}
	}
	if len(row) > 0 {
		buttons = append(buttons, row)
	}

	// Add navigation buttons if needed
	if totalPages > 1 {
		var navRow []telebot.InlineButton
		if currentPage > 0 {
			navRow = append(navRow, telebot.InlineButton{
				Text: "⬅️ Previous",
				Data: fmt.Sprintf("page:%s:%d", categoryID, currentPage-1),
			})
		}
		navRow = append(navRow, telebot.InlineButton{
			Text: fmt.Sprintf("📄 %d/%d", currentPage+1, totalPages),
			Data: "noop",
		})
		if currentPage < totalPages-1 {
			navRow = append(navRow, telebot.InlineButton{
				Text: "Next ➡️",
				Data: fmt.Sprintf("page:%s:%d", categoryID, currentPage+1),
			})
		}
		buttons = append(buttons, navRow)
	}

	// Add back and cart buttons
	var actionRow []telebot.InlineButton
	actionRow = append(actionRow, telebot.InlineButton{
		Text: "⬅️ Back to Categories",
		Data: "shop",
	})

	// Add cart button
	actionRow = append(actionRow, telebot.InlineButton{
		Text: "🛒 View Cart",
		Data: "cart:show", // Direct to cart handler to show cart
	})
	buttons = append(buttons, actionRow)

	return c.Edit(
		msg.String(),
		&telebot.ReplyMarkup{InlineKeyboard: buttons},
		telebot.ModeMarkdownV2,
	)
}

// showProductDetails shows product details and quantity selector
func (h *ShopHandler) showProductDetails(c telebot.Context, productID string) error {
	product := h.shopService.GetProduct(productID)
	if product == nil {
		return c.Edit("Product not found\\.")
	}

	session := h.sessions.GetSession(c.Sender().ID)

	// Initialize session data if needed
	if session.SelectedItems == nil {
		session.SelectedItems = make(map[string]interface{})
	}

	// Get current quantity, default to 1
	qty := 1
	if v, ok := session.SelectedItems["qty"]; ok {
		if qtyFloat, ok := v.(float64); ok { // Stored as float64 by some JSON decoders
			qty = int(qtyFloat)
		} else if qtyInt, ok := v.(int); ok { // Or as int
			qty = qtyInt
		}
	}

	// Store product ID in session
	session.SelectedItems["product"] = productID
	session.SelectedItems["qty"] = qty

	// Save session
	if err := h.sessions.SaveSession(session); err != nil {
		log.Printf("Error saving session: %v", err)
	}

	var msg strings.Builder
	msg.WriteString(fmt.Sprintf("*%s*\n\n", utils.EscapeMarkdownV2(product.Name)))
	msg.WriteString("```\n") // Start monospace block for fixed-width formatting
	msg.WriteString(fmt.Sprintf("Price: %.2f KES\n", product.Price))
	msg.WriteString(fmt.Sprintf("Stock: %d\n", product.StockQty))
	msg.WriteString(fmt.Sprintf("Selected Quantity: %d\n", qty))
	msg.WriteString(fmt.Sprintf("Subtotal: %.2f KES\n", float64(qty)*product.Price))
	msg.WriteString("```\n") // End monospace block

	var buttons [][]telebot.InlineButton

	// Quantity adjustment row
	buttons = append(buttons, []telebot.InlineButton{
		{Text: "➖", Data: fmt.Sprintf("qty:dec:%s", productID)},
		{Text: fmt.Sprintf("%d", qty), Data: "noop"}, // Display only, no action for the number itself
		{Text: "➕", Data: fmt.Sprintf("qty:inc:%s", productID)},
	})

	// Add to cart and view cart buttons
	// IMPORTANT: Changed "add:%s" to "cart:add:%s:%d" to be handled by CartHandler
	// Changed "cart" to "cart:show"
	buttons = append(buttons, []telebot.InlineButton{
		{Text: "🛒 Add to Cart", Data: fmt.Sprintf("cart:add:%s:%d", productID, qty)},
		{Text: "👀 View Cart", Data: "cart:show"},
	})

	// Back button
	buttons = append(buttons, []telebot.InlineButton{
		{Text: "⬅️ Back", Data: fmt.Sprintf("cat:%s", product.CategoryID)},
	})

	msgText := msg.String() // No need to escape the entire message since we're using monospace blocks

	return c.Edit(
		msgText,
		&telebot.ReplyMarkup{InlineKeyboard: buttons},
		telebot.ModeMarkdownV2,
	)
}

// handleQuantity handles quantity adjustment for a product
func (h *ShopHandler) handleQuantity(c telebot.Context) error {
	parts := strings.Split(c.Callback().Data, ":")
	if len(parts) < 2 {
		return fmt.Errorf("invalid callback data")
	}

	session := h.sessions.GetSession(c.Sender().ID)
	productID := parts[len(parts)-1]

	// Initialize session data if needed
	if session.SelectedItems == nil {
		session.SelectedItems = make(map[string]interface{})
	}

	// Get current quantity
	qty := 1
	if v, ok := session.SelectedItems["qty"]; ok {
		switch val := v.(type) {
		case int:
			qty = val
		case float64:
			qty = int(val)
		}
	}

	// Get product for stock check
	product := h.shopService.GetProduct(productID)
	if product == nil {
		return c.Respond(&telebot.CallbackResponse{
			Text:      "Product not found",
			ShowAlert: true,
		})
	}

	// Handle quantity adjustment
	if len(parts) > 2 {
		switch parts[1] {
		case "inc":
			if qty < product.StockQty {
				qty++
			} else {
				return c.Respond(&telebot.CallbackResponse{
					Text:      "Cannot exceed available stock",
					ShowAlert: true,
				})
			}
		case "dec":
			if qty > 1 {
				qty--
			}
		}
	}

	// Update session
	session.SelectedItems["product"] = productID
	session.SelectedItems["qty"] = qty

	// Save session
	if err := h.sessions.SaveSession(session); err != nil {
		log.Printf("Error saving session: %v", err)
		return c.Respond(&telebot.CallbackResponse{
			Text:      "Failed to update quantity",
			ShowAlert: true,
		})
	}

	return h.showProductDetails(c, productID)
}

// HandleCallback handles all callback queries
func (h *ShopHandler) HandleCallback(c telebot.Context) error {
	data := c.Callback().Data

	// Delegate to CartHandler if the callback data is prefixed with "cart:"
	if strings.HasPrefix(data, "cart:") {
		if h.cartHandler != nil {
			return h.cartHandler.HandleCartCallback(c)
		}
		log.Println("Error: cartHandler is not initialized in ShopHandler")
		return c.Respond(&telebot.CallbackResponse{Text: "Cart functionality is currently unavailable.", ShowAlert: true})
	}

	// Delegate to ProductHandler for product, quantity, or product-specific page callbacks
	if strings.HasPrefix(data, "product:") || strings.HasPrefix(data, "qty:") || (strings.HasPrefix(data, "page:") && strings.Contains(data, ":cat:")) { // page:cat:catID:pageNo
		if h.productHandler != nil {
			return h.productHandler.HandleProductCallback(c)
		}
		log.Println("Error: productHandler is not initialized in ShopHandler")
		return c.Respond(&telebot.CallbackResponse{Text: "Product functionality is currently unavailable.", ShowAlert: true})
	}

	// Delegate to CategoryHandler for category pagination callbacks
	if strings.HasPrefix(data, "catpage:") {
		if h.categoryHandler != nil {
			return h.categoryHandler.HandleCategoryCallback(c)
		}
		log.Println("Error: categoryHandler is not initialized in ShopHandler")
		return c.Respond(&telebot.CallbackResponse{Text: "Category navigation is currently unavailable.", ShowAlert: true})
	}

	parts := strings.Split(data, ":")
	if len(parts) == 0 {
		return c.Respond() // Should not happen if not caught by cart prefix
	}

	var err error
	switch parts[0] {
	case "shop": // Show main shop categories view (handled by categoryHandler)
		if h.categoryHandler != nil {
			err = h.categoryHandler.showCategories(c)
		} else {
			log.Println("Error: categoryHandler not initialized for 'shop' callback")
			err = c.Respond(&telebot.CallbackResponse{Text: "Shop categories unavailable.", ShowAlert: true})
		}

	case "cat": // Show products for a category (first page) - handled by ProductHandler
		if len(parts) > 1 {
			if h.productHandler != nil {
				// ProductHandler.showProducts expects categoryID and handles its own pagination state if any from callback
				err = h.productHandler.showProducts(c, parts[1])
			} else {
				log.Println("Error: productHandler not initialized for 'cat' callback")
				err = c.Respond(&telebot.CallbackResponse{Text: "Product view unavailable.", ShowAlert: true})
			}
		} else {
			err = fmt.Errorf("category ID missing in 'cat' callback: %s", data)
		}

	// "product:", "qty:", and "page:cat:" (product pagination) are handled by ProductHandler via prefix check above.
	// So, they are removed from this switch.

	// "page:" prefix for category pagination is now "catpage:" and handled by CategoryHandler by prefix check above.
	// "page:cat:" prefix for product pagination is handled by ProductHandler by prefix check above.
	// So, the general "page:" case can be removed.
	// If any other "page:..." format existed, it needs specific handling or to be considered an error.
	default:
		log.Printf("Unknown callback action in ShopHandler: %s", data)
		err = c.Respond(&telebot.CallbackResponse{
			Text:      "Unknown action.",
			ShowAlert: true,
		})
	}

	if err != nil {
		// Check if it's a "message not modified" error
		if strings.Contains(err.Error(), "message is not modified") {
			// Just respond to remove the loading state
			return c.Respond()
		}
		log.Printf("Error handling callback: %v", err)
		return c.Respond(&telebot.CallbackResponse{
			Text:      "An error occurred. Please try again.",
			ShowAlert: true,
		})
	}

	return c.Respond()
}
