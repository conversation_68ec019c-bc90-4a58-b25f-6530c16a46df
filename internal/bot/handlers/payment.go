package handlers

import (
	"context"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/itunza/telegram-silcore/internal/services"
	"github.com/itunza/telegram-silcore/internal/types"
	"github.com/itunza/telegram-silcore/internal/utils"
	"gopkg.in/telebot.v3"
)

type PaymentHandler struct {
	paymentService *services.PaymentService
	sessions       *services.SessionService
}

func NewPaymentHandler(
	paymentService *services.PaymentService,
	sessions *services.SessionService,
) *PaymentHandler {
	return &PaymentHandler{
		paymentService: paymentService,
		sessions:       sessions,
	}
}

func (h *PaymentHandler) RegisterHandlers(bot *telebot.Bot) {
	bot.Handle("/pay", h.HandlePayCommand)
	// Add other payment-related command handlers here
}

func (h *PaymentHandler) HandlePayCommand(c telebot.Context) error {
	session := h.sessions.GetSession(c.Sender().ID)
	if session == nil || !session.IsAuthenticated() {
		return c.Send("⚠️ Please login first to make payments.")
	}

	customer := session.GetActiveCustomer()
	if customer == nil || len(customer.Cart) == 0 {
		return c.Send("Your cart is empty. Add some items first!")
	}

	msg := &strings.Builder{}
	msg.WriteString("*Select Payment Method*\n\n")
	msg.WriteString("Choose how you would like to pay:")

	// Create payment method selection buttons
	buttons := [][]telebot.InlineButton{
		{{Text: "💳 M-Pesa", Data: "pay:mpesa"}},
		{{Text: "📊 On Account", Data: "pay:account"}},
		{{Text: "🔙 Cancel", Data: "pay:cancel"}},
	}

	return c.Send(
		msg.String(),
		&telebot.ReplyMarkup{InlineKeyboard: buttons},
		telebot.ModeMarkdownV2,
	)
}

func (h *PaymentHandler) HandleCallback(c telebot.Context) error {
	data := c.Callback().Data
	if !strings.HasPrefix(data, "pay:") && !strings.HasPrefix(data, "payment:") {
		return nil
	}

	parts := strings.Split(data, ":")
	if len(parts) < 2 {
		return nil
	}

	action := parts[1]
	session := h.sessions.GetSession(c.Sender().ID)
	if session == nil || !session.IsAuthenticated() {
		return c.Respond(&telebot.CallbackResponse{
			Text:      "Please login first",
			ShowAlert: true,
		})
	}
	customer := session.GetActiveCustomer()
	if customer == nil || len(customer.Cart) == 0 {
		return c.Respond(&telebot.CallbackResponse{
			Text:      "Your cart is empty",
			ShowAlert: true,
		})
	}
	switch action {
	case "mpesa":
		return h.handleMPesaPayment(c)
	case "account":
		return h.handleAccountPayment(c)

	case "retry":
		return h.handlePaymentRetry(c)

	case "cancel":
		if len(parts) < 3 {
			return c.Respond(&telebot.CallbackResponse{
				Text:      "Invalid cancel request",
				ShowAlert: true,
			})
		}
		paymentID := parts[2]
		return h.handlePaymentCancel(c, paymentID)
	default:
		return c.Respond(&telebot.CallbackResponse{
			Text:      "Invalid payment action",
			ShowAlert: true,
		})
	}
}

func (h *PaymentHandler) handleMPesaPayment(c telebot.Context) error {
	msg := "Please enter M-Pesa phone number (format: 254XXXXXXXXX 07XXXXXXXX) :"

	// Update session state
	session := h.sessions.GetSession(c.Sender().ID)
	if session == nil {
		return c.Reply("⚠️ Session expired. Please start over with /start")
	}

	session.CurrentMenu = "payment_mpesa"
	session.AwaitingSearch = true // Use this to indicate waiting for phone number
	if err := h.sessions.SaveSession(session); err != nil {
		return fmt.Errorf("failed to save session: %v", err)
	}
	// Delete the message after 15 seconds
	go func() {
		time.Sleep(15 * time.Second)
		if err := c.Delete(); err != nil {
			log.Printf("Failed to delete user's phone number message: %v", err)
		}
	}()

	return c.Edit(msg)
}

func (h *PaymentHandler) handleAccountPayment(c telebot.Context) error {
	session := h.sessions.GetSession(c.Sender().ID)
	customer := session.GetActiveCustomer()
	if customer == nil {
		return c.Edit("No active customer session found.")
	}

	if customer.CustomerCode == "" {
		return c.Edit(
			"You don't have an account set up for credit purchases.",
			&telebot.ReplyMarkup{InlineKeyboard: [][]telebot.InlineButton{
				{{Text: "🔙 Back", Data: "pay"}},
			}},
		)
	}

	msg := "Confirm payment on account?"
	buttons := [][]telebot.InlineButton{
		{{Text: "✅ Confirm", Data: "pay:account:confirm"}},
		{{Text: "❌ Cancel", Data: "pay:cancel"}},
	}

	return c.Edit(
		msg,
		&telebot.ReplyMarkup{InlineKeyboard: buttons},
	)
}

// HandleMPesaPhoneNumber processes the phone number input for M-Pesa payment
func (h *PaymentHandler) HandleMPesaPhoneNumber(c telebot.Context) error {
	go func() {
		time.Sleep(5 * time.Second)
		if err := c.Delete(); err != nil {
			log.Printf("Failed to delete user's phone number message: %v", err)
		}
	}()

	phoneNumber := strings.TrimSpace(c.Text())

	// Reset the awaiting state
	session := h.sessions.GetSession(c.Sender().ID)
	if session == nil {
		return c.Reply("⚠️ Session expired. Please start over with /start")
	}

	// Validate session state
	if session.CurrentMenu != "payment_mpesa" {
		return nil // Not waiting for phone number
	}

	log.Printf("Processing M-Pesa phone number: %s", phoneNumber)

	// Reset the awaiting state
	session.AwaitingSearch = false
	session.CurrentMenu = ""
	if err := h.sessions.SaveSession(session); err != nil {
		log.Printf("Error saving session: %v", err)
	}

	// Sanitize phone number to handle different formats
	sanitizedPhone := utils.SanitizePhone(phoneNumber)
	if sanitizedPhone == "" {
		return c.Reply("⚠️ Invalid phone number format. Please use format:\n" +
			"• 254XXXXXXXXX\n" +
			"• 07XXXXXXXX\n" +
			"• 01XXXXXXXX\n\n" +
			"Try again or click /cancel to abort.")
	}

	customerSession := session.GetActiveCustomer()
	if customerSession == nil || len(customerSession.Cart) == 0 {
		return c.Reply("⚠️ No active cart found. Please start over.")
	}

	// Show processing message and capture the message for later editing
	processingMsg, err := c.Bot().Send(c.Recipient(), "Processing your M-Pesa payment request...")
	if err != nil {
		log.Printf("Error sending processing message: %v", err)
		return c.Reply("⚠️ Failed to process payment. Please try again.")
	}

	// Convert cart items
	convertedCart := make(map[string]*types.CartItem)
	for k, v := range customerSession.Cart {
		convertedCart[k] = &types.CartItem{
			ProductID: v.ProductID,
			Quantity:  v.Quantity,
			Price:     v.Price,
			Warehouse: v.Warehouse,
		}
	}

	// Convert customer
	customer := &types.Customer{
		ID:           customerSession.CustomerID,   // Use the actual customer ID from session
		CustomerCode: customerSession.CustomerCode, // Use the actual customer code from session
		Cart:         convertedCart,
		Mobile:       phoneNumber,
	}

	// Create payment context with necessary information
	paymentCtx := &types.PaymentContext{
		UserID:    c.Sender().ID,
		ChatID:    c.Chat().ID,
		MessageID: processingMsg.ID,
		Customer:  customer,
	}

	// Initiate the payment with the payment context
	if err := h.paymentService.InitiateMPesaPayment(context.Background(), paymentCtx); err != nil {
		log.Printf("Error initiating M-Pesa payment: %v", err)

		errorMsg := "⚠️ Failed to initiate M-Pesa payment. Please try again later."
		if processingMsg != nil {
			_, editErr := c.Bot().Edit(
				processingMsg,
				errorMsg,
				&telebot.ReplyMarkup{
					InlineKeyboard: [][]telebot.InlineButton{
						{{Text: "🔙 Back to Cart", Data: "view_cart"}},
					},
				},
			)
			if editErr != nil {
				log.Printf("Error editing message: %v", editErr)
			}
		} else {
			// Fallback to sending a new message
			return c.Reply(errorMsg)
		}
		return nil
	}

	return nil
}

// Add this new method to handle payment cancellation
func (h *PaymentHandler) handlePaymentCancel(c telebot.Context, paymentID string) error {
	// Cancel the payment using background context
	err := h.paymentService.CancelPayment(context.Background(), paymentID)
	if err != nil {
		switch err.Error() {
		case "payment_timeout":
			// Payment has timed out, show cart view
			return h.showCart(c)
		case "payment_pending":
			// Payment is still within 5-minute window
			return c.Edit(
				"Payment is still being processed. Please wait for 5 minutes before cancelling.",
				&telebot.ReplyMarkup{InlineKeyboard: [][]telebot.InlineButton{
					{
						{Text: "🔄 Check Status", Data: fmt.Sprintf("payment:status:%s", paymentID)},
						{Text: "❌ Cancel", Data: fmt.Sprintf("payment:cancel:%s", paymentID)},
					},
					{{Text: "🛒 View Cart", Data: "view_cart"}},
				}},
			)
		default:
			// Other errors - show error with cart view option
			return c.Edit(
				"Failed to cancel payment. Please try again.",
				&telebot.ReplyMarkup{InlineKeyboard: [][]telebot.InlineButton{
					{
						{Text: "❌ Cancel", Data: fmt.Sprintf("payment:cancel:%s", paymentID)},
					},
					{{Text: "🛒 View Cart", Data: "view_cart"}},
				}},
			)
		}
	}

	// If successfully cancelled, show cart
	return h.showCart(c)
}

// Add this helper method to show cart
func (h *PaymentHandler) showCart(c telebot.Context) error {
	session := h.sessions.GetSession(c.Sender().ID)
	customer := session.GetActiveCustomer()
	if customer == nil {
		return c.Edit("No active cart found. Please start shopping.",
			&telebot.ReplyMarkup{InlineKeyboard: [][]telebot.InlineButton{
				{{Text: "🏪 Go to Shop", Data: "shop"}},
			}},
		)
	}

	// Build cart message
	msg := &strings.Builder{}
	msg.WriteString("🛒 *Your Cart*\n\n")

	total := 0.0
	for _, item := range customer.Cart {
		itemTotal := item.Price * float64(item.Quantity)
		total += itemTotal
		msg.WriteString(fmt.Sprintf("• %s (x%d) - %.2f\n", item.ProductID, item.Quantity, itemTotal))
	}
	msg.WriteString(fmt.Sprintf("\n*Total: %.2f*", total))

	// Create buttons
	buttons := [][]telebot.InlineButton{
		{{Text: "✅ Checkout", Data: "checkout"}},
		{{Text: "🏪 Continue Shopping", Data: "shop"}},
		{{Text: "�� Clear Cart", Data: "cart:clear"}},
	}

	return c.Edit(
		msg.String(),
		&telebot.ReplyMarkup{InlineKeyboard: buttons},
		telebot.ModeMarkdownV2,
	)
}

// handlePaymentRetry handles the retry of a failed payment
func (h *PaymentHandler) handlePaymentRetry(c telebot.Context) error {
	// Send processing message
	processingMsg, err := c.Bot().Edit(
		c.Message(),
		"Processing payment...",
		&telebot.ReplyMarkup{},
	)
	if err != nil {
		log.Printf("Error sending processing message: %v", err)
		return err
	}

	// Get the payment ID from the callback data
	paymentID := strings.TrimPrefix(c.Callback().Data, "payment:retry:")

	// Get the existing payment to get the phone number
	existingPayment, err := h.paymentService.GetPaymentStatus(context.Background(), paymentID)
	if err != nil {
		log.Printf("Error getting existing payment: %v", err)
		return c.Edit("Failed to retrieve payment information. Please try again.")
	}

	// Get customer information from session
	session := h.sessions.GetSession(c.Sender().ID)
	customerSession := session.GetActiveCustomer()
	if customerSession == nil {
		return c.Edit("No active customer found. Please start over.")
	}

	// Convert cart items
	convertedCart := make(map[string]*types.CartItem)
	for k, v := range customerSession.Cart {
		convertedCart[k] = &types.CartItem{
			ProductID: v.ProductID,
			Quantity:  v.Quantity,
			Price:     v.Price,
			Warehouse: v.Warehouse,
		}
	}

	// Convert customer using phone number from existing payment
	customer := &types.Customer{
		ID:           customerSession.CustomerID,
		CustomerCode: customerSession.CustomerCode,
		Cart:         convertedCart,
		Mobile:       existingPayment.PhoneNumber, // Use phone number from existing payment
	}

	// Create payment context with necessary information
	paymentCtx := &types.PaymentContext{
		UserID:    c.Sender().ID,
		ChatID:    c.Chat().ID,
		MessageID: processingMsg.ID,
		Customer:  customer,
		PaymentID: paymentID, // Use the existing payment ID for retry
	}

	// Initiate the payment with the payment context
	if err := h.paymentService.InitiateMPesaPayment(context.Background(), paymentCtx); err != nil {
		log.Printf("Error initiating M-Pesa payment: %v", err)

		// Edit the processing message with the error
		_, editErr := c.Bot().Edit(
			processingMsg,
			"⚠️ Failed to initiate M-Pesa payment. Please try again later.",
			&telebot.ReplyMarkup{
				InlineKeyboard: [][]telebot.InlineButton{
					{{Text: "🔙 Back to Cart", Data: "view_cart"}},
				},
			},
		)
		if editErr != nil {
			log.Printf("Error editing message: %v", editErr)
		}
		return nil
	}

	return nil
}

// Register the handlers
func (h *PaymentHandler) Register(bot *telebot.Bot) {
	bot.Handle("/pay", h.HandlePayCommand)
	// Add other payment-related command handlers here
	bot.Handle(&telebot.InlineButton{Data: "payment:retry:"}, h.handlePaymentRetry)
}
