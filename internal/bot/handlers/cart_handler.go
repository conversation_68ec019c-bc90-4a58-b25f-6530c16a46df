package handlers

import (
	"fmt"
	"log"
	"strings"

	// strconv will be needed if any cart actions involve parsing numbers from callbacks, like quantity for "cart:add:productID:qty"
	// "strconv"

	"github.com/itunza/telegram-silcore/internal/bot/utils"
	"github.com/itunza/telegram-silcore/internal/services"
	telebot "gopkg.in/telebot.v3"
)

// CartHandler handles cart-related operations.
// It will use ShopService for product information and SessionService for managing user carts.
type CartHandler struct {
	shopService *services.ShopService
	sessions    *services.SessionService
}

// NewCartHandler creates a new CartHandler instance.
func NewCartHandler(shopService *services.ShopService, sessions *services.SessionService) *CartHandler {
	return &CartHandler{
		shopService: shopService,
		sessions:    sessions,
	}
}

// HandleCartCallback is the main router for cart-related callback queries.
// It expects callback data like "cart:action:payload"
// e.g., "cart:show", "cart:add:product123", "cart:remove:product456", "cart:clear", "cart:checkout"
func (h *CartHandler) HandleCartCallback(c telebot.Context) error {
	callbackData := c.Callback().Data
	parts := strings.Split(callbackData, ":")

	if len(parts) < 2 || parts[0] != "cart" {
		log.Printf("Invalid or non-cart callback data received: %s", callbackData)
		return c.Respond(&telebot.CallbackResponse{
			Text:      "Invalid action.",
			ShowAlert: true,
		})
	}

	action := parts[1]
	var err error
	var productID string

	if len(parts) > 2 {
		productID = parts[2] // Used for add, remove
		// If quantity is needed for 'add', it would be parts[3]
		// Example: "cart:add:productID:qty"
		// qty, _ := strconv.Atoi(parts[3])
	}

	switch action {
	case "show":
		err = h.showCart(c)
	case "add":
		if productID == "" {
			log.Printf("Missing productID for cart:add action: %s", callbackData)
			err = c.Respond(&telebot.CallbackResponse{Text: "Error: Product ID missing.", ShowAlert: true})
		} else {
			// Note: The current handleAddToCart expects quantity from session.SelectedItems.
			// If "cart:add:productID:qty" is used, handleAddToCart might need adjustment
			// or quantity needs to be set to session here before calling.
			// For now, it matches the existing structure where showProductDetails sets the qty in session.
			err = h.handleAddToCart(c, productID)
		}
	case "remove":
		if productID == "" {
			log.Printf("Missing productID for cart:remove action: %s", callbackData)
			err = c.Respond(&telebot.CallbackResponse{Text: "Error: Product ID missing.", ShowAlert: true})
		} else {
			err = h.handleCartAction(c, "remove", productID)
		}
	case "clear":
		err = h.handleCartAction(c, "clear", "")
	case "checkout":
		err = h.handleCartAction(c, "checkout", "")
	default:
		log.Printf("Unknown cart action received: %s", action)
		err = c.Respond(&telebot.CallbackResponse{
			Text:      "Unknown cart action.",
			ShowAlert: true,
		})
	}

	if err != nil {
		// Check if the error is from c.Respond() or c.Edit() itself, or if it's a logic error
		// For now, just log it. Specific error handling can be more granular.
		log.Printf("Error processing cart callback action '%s': %v (CallbackData: %s)", action, err, callbackData)
		// Avoid sending another response if one might have already been sent by the sub-handlers on error.
		// However, if `err` is from a service call and no response was sent, this is a good place for a generic error.
		// For simplicity here, we assume sub-handlers deal with their own c.Respond() on specific errors.
	}

	// If no error occurred and no response/edit was sent by sub-handlers,
	// it's good practice to respond to the callback to remove the loading spinner.
	// However, all current paths either edit the message or respond.
	// If a path doesn't, then a c.Respond() here would be needed.
	// Example: if err == nil && !c.IsResponded() { return c.Respond() }
	return nil // Errors are handled by responding to the user or logging.
}

// showCart displays the current shopping cart
func (h *CartHandler) showCart(c telebot.Context) error {
	session := h.sessions.GetSession(c.Sender().ID)
	if session.Cart == nil || len(session.Cart) == 0 {
		return c.Edit(
			"Your cart is empty\\. Use /shop to browse products\\.",
			&telebot.ReplyMarkup{InlineKeyboard: [][]telebot.InlineButton{
				{{Text: "🏪 Browse Products", Data: "shop"}}, // "shop" callback should lead to shop categories
			}},
			telebot.ModeMarkdownV2,
		)
	}

	var msg strings.Builder
	msg.WriteString("*Shopping Cart* 🛒\n\n")
	msg.WriteString("```\n") // Start monospace block

	maxNameLen := 0
	var total float64
	for productID := range session.Cart {
		product := h.shopService.GetProduct(productID)
		if product != nil && len(product.Name) > maxNameLen {
			maxNameLen = len(product.Name)
		}
	}

	headerFormat := fmt.Sprintf("%%-%ds  %%8s  %%8s  %%10s\n", maxNameLen)
	msg.WriteString(fmt.Sprintf(headerFormat, "Product", "Price", "Qty", "Subtotal"))
	msg.WriteString(strings.Repeat("-", maxNameLen+30) + "\n")

	itemFormat := fmt.Sprintf("%%-%ds  %%8.2f  %%8d  %%10.2f\n", maxNameLen)
	for productID, qty := range session.Cart {
		product := h.shopService.GetProduct(productID)
		if product == nil {
			continue // Should ideally not happen if cart is managed well
		}
		subtotal := float64(qty) * product.Price
		total += subtotal
		msg.WriteString(fmt.Sprintf(itemFormat,
			utils.EscapeMarkdownV2(product.Name), // Assuming product names might need escaping
			product.Price,
			qty,
			subtotal,
		))
	}

	msg.WriteString(strings.Repeat("-", maxNameLen+30) + "\n")
	msg.WriteString(fmt.Sprintf(fmt.Sprintf("%%%ds  %%10.2f\n", maxNameLen+19), // Adjusted padding for "Total:"
		"Total:",
		total,
	))
	msg.WriteString("```\n") // End monospace block

	var buttons [][]telebot.InlineButton
	for productID, qty := range session.Cart {
		product := h.shopService.GetProduct(productID)
		if product == nil {
			continue
		}
		// Button to remove item
		buttons = append(buttons, []telebot.InlineButton{
			{Text: fmt.Sprintf("❌ Remove %s", utils.EscapeMarkdownV2(product.Name)), Data: fmt.Sprintf("cart:remove:%s", productID)},
		})
		// Button to edit quantity (leads back to product details view)
		// The product details view should be handled by ShopHandler still, or a new ProductHandler.
		// For now, this button takes user to product view, assuming ShopHandler's "product:%s" callback.
		buttons = append(buttons, []telebot.InlineButton{
			{Text: fmt.Sprintf("📝 Edit %s (%d)", utils.EscapeMarkdownV2(product.Name), qty), Data: fmt.Sprintf("product:%s", productID)},
		})
	}

	buttons = append(buttons, []telebot.InlineButton{
		{Text: "🗑️ Clear Cart", Data: "cart:clear"},
		{Text: "✅ Checkout", Data: "cart:checkout"}, // Checkout might be a separate handler later
	})
	buttons = append(buttons, []telebot.InlineButton{
		{Text: "🏪 Continue Shopping", Data: "shop"}, // Back to shop categories
	})

	return c.Edit(
		msg.String(),
		&telebot.ReplyMarkup{InlineKeyboard: buttons},
		telebot.ModeMarkdownV2,
	)
}

// handleAddToCart handles adding or updating items in cart
func (h *CartHandler) handleAddToCart(c telebot.Context, productID string) error {
	session := h.sessions.GetSession(c.Sender().ID)

	// Quantity is expected to be part of the callback data for adding to cart, e.g., "cart:add:productID:qty"
	// Or, it's retrieved from a temporary state in the session, set by showProductDetails.
	// The original handleAddToCart in ShopHandler retrieved qty from session.SelectedItems.
	// We'll assume that `session.SelectedItems["qty"]` is still populated by `ShopHandler.showProductDetails`
	// before this handler is called via a callback like `cart:add:productID`.
	// This is a dependency on how ShopHandler sets up the session state.
	// The callback "cart:add:productID:qty" is now set in shopHandler.showProductDetails
	// So, we should ideally parse qty from callback here.

	var qty int
	callbackData := c.Callback().Data
	parts := strings.Split(callbackData, ":") // e.g. "cart:add:productID:qty"

	if len(parts) == 4 && parts[0] == "cart" && parts[1] == "add" {
		// TODO: Add strconv import and error handling for Atoi
		// qty, _ = strconv.Atoi(parts[3])
		// For now, we rely on session as per original logic until strconv is added.
		if v, ok := session.SelectedItems["qty"]; ok {
			switch val := v.(type) {
			case int:
				qty = val
			case float64:
				qty = int(val)
			default:
				log.Printf("Warning: Unexpected type for quantity in session: %T for product %s", v, productID)
				qty = 1
			}
		} else {
			qty = 1 // Default if not in session (though it should be)
		}

	} else {
		// Fallback or if callback format is just "cart:add:productID"
		if v, ok := session.SelectedItems["qty"]; ok {
			switch val := v.(type) {
			case int:
				qty = val
			case float64:
				qty = int(val)
			default:
				log.Printf("Warning: Unexpected type for quantity in session: %T for product %s", v, productID)
				qty = 1
			}
		} else {
			qty = 1 // Default if not in session
		}
	}

	if qty <= 0 {
		qty = 1 // Default to 1 if quantity is invalid
	}

	product := h.shopService.GetProduct(productID)
	if product == nil {
		return c.Respond(&telebot.CallbackResponse{
			Text:      "Product not found",
			ShowAlert: true,
		})
	}

	if qty > product.StockQty {
		return c.Respond(&telebot.CallbackResponse{
			Text:      "Requested quantity exceeds available stock",
			ShowAlert: true,
		})
	}

	if session.Cart == nil {
		session.Cart = make(map[string]int)
	}
	session.Cart[productID] = qty

	if err := h.sessions.SaveSession(session); err != nil {
		log.Printf("Error saving session in handleAddToCart: %v", err)
		return c.Respond(&telebot.CallbackResponse{
			Text:      "Failed to update cart",
			ShowAlert: true,
		})
	}

	// Respond to the callback first
	err := c.Respond(&telebot.CallbackResponse{
		Text:      fmt.Sprintf("%s added to cart!", utils.EscapeMarkdownV2(product.Name)),
		ShowAlert: false,
	})
	if err != nil {
		log.Printf("Error responding to callback in handleAddToCart: %v", err)
	}

	return h.showCart(c)
}

// handleCartAction handles various cart actions (remove, clear, checkout)
func (h *CartHandler) handleCartAction(c telebot.Context, action string, productID string) error {
	session := h.sessions.GetSession(c.Sender().ID)

	if session.Cart == nil && (action == "remove" || action == "checkout") {
		return c.Respond(&telebot.CallbackResponse{
			Text:      "Your cart is already empty.",
			ShowAlert: true,
		})
	}
	if session.Cart == nil {
		session.Cart = make(map[string]int)
	}

	var err error
	alertText := ""

	switch action {
	case "remove":
		if _, exists := session.Cart[productID]; exists {
			delete(session.Cart, productID)
			alertText = "Item removed from cart."
		} else {
			alertText = "Item not found in cart."
		}
	case "clear":
		session.Cart = make(map[string]int)
		alertText = "Cart cleared."
	case "checkout":
		if len(session.Cart) == 0 {
			return c.Edit(
				"Your cart is empty. Nothing to checkout.",
				&telebot.ReplyMarkup{InlineKeyboard: [][]telebot.InlineButton{
					{{Text: "🏪 Back to Shop", Data: "shop"}},
				}},
				telebot.ModeMarkdownV2,
			)
		}
		session.Cart = make(map[string]int)
		log.Printf("User %d proceeded to checkout. Cart cleared.", c.Sender().ID)

		err = h.sessions.SaveSession(session)
		if err != nil {
			log.Printf("Error saving session after checkout: %v", err)
		}

		return c.Edit(
			"Thank you for your order! We will process it shortly. (Simulated Checkout)",
			&telebot.ReplyMarkup{InlineKeyboard: [][]telebot.InlineButton{
				{{Text: "🏪 Back to Shop", Data: "shop"}},
			}},
			telebot.ModeMarkdownV2,
		)
	default:
		// This case should ideally not be reached if HandleCartCallback filters actions
		return c.Respond(&telebot.CallbackResponse{
			Text:      "Unknown cart action.",
			ShowAlert: true,
		})
	}

	if err := h.sessions.SaveSession(session); err != nil {
		log.Printf("Error saving session after cart action '%s': %v", action, err)
		return c.Respond(&telebot.CallbackResponse{
			Text:      "Failed to update cart. Please try again.",
			ShowAlert: true,
		})
	}

	if alertText != "" {
		// Try to respond to the callback to confirm the action
		// It's possible an edit (like showCart) will follow, which is fine.
		respErr := c.Respond(&telebot.CallbackResponse{Text: alertText})
		if respErr != nil {
			log.Printf("Error responding to callback for action %s: %v", action, respErr)
		}
	}

	// After action, refresh cart view (unless it was checkout which already edits the message)
	if action == "remove" || action == "clear" {
		return h.showCart(c)
	}

	return err // Return original error if any from save session or other steps
}
