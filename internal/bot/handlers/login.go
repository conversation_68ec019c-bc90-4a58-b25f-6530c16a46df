package handlers

import (
	"log"
	"regexp"
	"strings"

	"github.com/itunza/telegram-silcore/internal/services"
	telebot "gopkg.in/telebot.v3"
)

// LoginHandler handles user login
type LoginHandler struct {
	authService *services.AuthService
}

// NewLoginHandler creates a new login handler
func NewLoginHandler(authService *services.AuthService) *LoginHandler {
	return &LoginHandler{
		authService: authService,
	}
}

// HandleLogin handles the login process
func (h *LoginHandler) HandleLogin(c telebot.Context) error {
	// Get user's contact info
	contact := c.Message().Contact
	if contact == nil {
		// Create a keyboard with the contact sharing button
		keyboard := &telebot.ReplyMarkup{
			ResizeKeyboard:  true,
			OneTimeKeyboard: true,
		}

		// Create a contact request button
		contactBtn := keyboard.Contact("📱 Share Contact")
		keyboard.Reply(
			keyboard.Row(contactBtn),
		)

		return c.Send("To verify your identity, please share your contact by clicking the button below:", keyboard)
	}

	// Normalize phone number
	phone := normalizePhone(contact.PhoneNumber)
	if phone == "" {
		return c.Send("❌ Invalid phone number format. Please try again.")
	}

	// Attempt to login
	_, err := h.authService.LoginUser(c.Sender().ID, phone)
	if err != nil {
		log.Printf("Login failed for %s: %v", phone, err)
		return c.Send("🔒 Login failed. Please ensure your phone number is registered or contact support.")
	}

	// Remove the keyboard after successful login
	removeKeyboard := &telebot.ReplyMarkup{
		RemoveKeyboard: true,
	}

	return c.Send("✅ Login successful! You can now use the shop features.", removeKeyboard)
}

// HandleLogout handles the logout process
func (h *LoginHandler) HandleLogout(c telebot.Context) error {
	err := h.authService.LogoutUser(c.Sender().ID)
	if err != nil {
		log.Printf("Logout failed for user %d: %v", c.Sender().ID, err)
		return c.Send("❌ Failed to logout. Please try again.")
	}

	return c.Send("👋 You have been logged out successfully. Use /login to log back in.")
}

// RegisterHandlers registers all login-related handlers
func (h *LoginHandler) RegisterHandlers(bot *telebot.Bot) {
	bot.Handle("/login", h.HandleLogin)
	bot.Handle("/logout", h.HandleLogout)
	bot.Handle(telebot.OnContact, h.HandleLogin)
	bot.Handle(telebot.OnCallback, h.HandleCallback)
}

// HandleCallback handles login-related callbacks
func (h *LoginHandler) HandleCallback(c telebot.Context) error {
	data := c.Callback().Data
	if data != "login" {
		return nil // Not our callback
	}

	// Acknowledge the callback to remove loading indicator
	if err := c.Respond(); err != nil {
		log.Printf("Error responding to callback: %v", err)
	}

	// Create a keyboard with the contact sharing button
	keyboard := &telebot.ReplyMarkup{
		ResizeKeyboard:  true,
		OneTimeKeyboard: true,
	}

	// Create a contact request button
	contactBtn := keyboard.Contact("📱 Share Contact")
	keyboard.Reply(
		keyboard.Row(contactBtn),
	)

	return c.Send("To verify your identity, please share your contact by clicking the button below:", keyboard)
}

// normalizePhone normalizes a phone number to E.164 format
func normalizePhone(phone string) string {
	// Remove all non-digit characters
	reg := regexp.MustCompile(`[^\d]`)
	phone = reg.ReplaceAllString(phone, "")

	// Handle Kenyan numbers
	if strings.HasPrefix(phone, "0") {
		phone = "254" + phone[1:]
	} else if strings.HasPrefix(phone, "254") {
		// Already in correct format
	} else if len(phone) == 9 {
		phone = "254" + phone
	} else {
		return ""
	}

	return phone
}
