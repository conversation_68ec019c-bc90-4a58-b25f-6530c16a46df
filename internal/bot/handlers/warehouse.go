package handlers

import (
	"fmt"
	"log"
	"strings"

	"github.com/itunza/telegram-silcore/internal/services"
	telebot "gopkg.in/telebot.v3"
)

// WarehouseHandler handles warehouse-related commands
type WarehouseHandler struct {
	shopService *services.ShopService
	authService *services.AuthService
}

func NewWarehouseHandler(shop *services.ShopService, auth *services.AuthService) *WarehouseHandler {
	return &WarehouseHandler{
		shopService: shop,
		authService: auth,
	}
}

// RegisterHandlers registers all warehouse-related handlers
func (h *WarehouseHandler) RegisterHandlers(bot *telebot.Bot) {
	bot.Handle("/warehouse", h.HandleWarehouseCommand)
	bot.Handle(telebot.OnCallback, h.<PERSON>)
}

func (h *WarehouseHandler) HandleWarehouseCommand(c telebot.Context) error {
	// Validate session first
	if !h.authService.ValidateSession(c.Sender().ID) {
		return c.Send(
			"⚠️ Please login first using /login to access warehouse settings.",
			&telebot.ReplyMarkup{InlineKeyboard: [][]telebot.InlineButton{
				{{Text: "🔑 Login", Data: "login"}},
			}},
		)
	}

	// Get all available warehouses from ERPNext
	warehouses, err := h.shopService.GetUserWarehouses(c.Sender().ID)
	if err != nil {
		log.Printf("Error fetching warehouses for user %d: %v", c.Sender().ID, err)
		return c.Send("Failed to fetch warehouses. Please try again later.")
	}

	var msg strings.Builder
	msg.WriteString("🏭 *Available Warehouses*\n\n")

	if len(warehouses) == 0 {
		msg.WriteString("No warehouses available.")
		return c.Send(msg.String(), telebot.ModeMarkdown)
	}

	var buttons [][]telebot.InlineButton
	var row []telebot.InlineButton

	for _, w := range warehouses {
		defaultMark := ""
		if w.IsDefault {
			defaultMark = " ✓"
		}
		msg.WriteString(fmt.Sprintf("• %s%s\n", w.WarehouseID, defaultMark))

		btn := telebot.InlineButton{
			Text: w.WarehouseID,
			Data: fmt.Sprintf("wh:%s", w.WarehouseID),
		}
		row = append(row, btn)

		if len(row) == 2 {
			buttons = append(buttons, row)
			row = nil
		}
	}

	if len(row) > 0 {
		buttons = append(buttons, row)
	}

	buttons = append(buttons, []telebot.InlineButton{{
		Text: "🔄 Refresh List",
		Data: "wh:refresh",
	}})

	return c.Send(msg.String(),
		&telebot.ReplyMarkup{InlineKeyboard: buttons},
		telebot.ModeMarkdown)
}

func (h *WarehouseHandler) HandleCallback(c telebot.Context) error {
	// Validate session for callbacks too
	if !h.authService.ValidateSession(c.Sender().ID) {
		// For callbacks, we need to respond to prevent the loading indicator
		if err := c.Respond(); err != nil {
			log.Printf("Error responding to callback: %v", err)
		}

		// Edit the message to show login requirement
		err := c.Edit(
			"⚠️ Your session has expired\\. Please log in again using /login\\.",
			&telebot.ReplyMarkup{InlineKeyboard: [][]telebot.InlineButton{
				{{Text: "🔑 Login", Data: "login"}},
			}},
			telebot.ModeMarkdownV2,
		)

		if err != nil && !strings.Contains(err.Error(), "message is not modified") {
			log.Printf("Error editing message: %v", err)
		}
		return nil
	}

	data := c.Callback().Data
	if !strings.HasPrefix(data, "wh:") {
		return nil
	}

	action := strings.TrimPrefix(data, "wh:")

	if action == "refresh" {
		return h.HandleWarehouseCommand(c)
	}

	// Set the selected warehouse
	err := h.shopService.SetUserWarehouse(c.Sender().ID, action)
	if err != nil {
		log.Printf("Error setting warehouse for user %d: %v", c.Sender().ID, err)
		return c.Respond(&telebot.CallbackResponse{
			Text:      "Failed to set warehouse. Please try again.",
			ShowAlert: true,
		})
	}

	text := fmt.Sprintf("✅ Selected warehouse: *%s*", action)
	return c.Edit(text, &telebot.ReplyMarkup{}, telebot.ModeMarkdown)
}
