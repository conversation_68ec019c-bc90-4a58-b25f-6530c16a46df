package handlers

import (
	"fmt"
	"log"
	"strconv"
	"strings"

	"github.com/itunza/telegram-silcore/internal/bot/utils"
	"github.com/itunza/telegram-silcore/internal/services"
	telebot "gopkg.in/telebot.v3"
)

// ProductHandler handles operations related to displaying product lists and product details.
type ProductHandler struct {
	shopService *services.ShopService
	sessions    *services.SessionService
}

// NewProductHandler creates a new ProductHandler instance.
func NewProductHandler(shopService *services.ShopService, sessions *services.SessionService) *ProductHandler {
	return &ProductHandler{
		shopService: shopService,
		sessions:    sessions,
	}
}

// HandleProductCallback routes callbacks for product-related actions.
// Expected callback data formats:
// - "product:<productID>"
// - "qty:<action>:<productID>"
// - "page:cat:<categoryID>:<page>" (for product pagination)
func (h *ProductHandler) HandleProductCallback(c telebot.Context) error {
	callbackData := c.Callback().Data
	parts := strings.Split(callbackData, ":")

	if len(parts) < 1 {
		log.Printf("Invalid callback data (empty): %s", callbackData)
		return c.Respond(&telebot.CallbackResponse{
			Text:      "Invalid action.",
			ShowAlert: true,
		})
	}

	prefix := parts[0]
	var err error

	switch prefix {
	case "product": // "product:<productID>"
		if len(parts) < 2 || parts[1] == "" {
			log.Printf("Missing productID for 'product:' action: %s", callbackData)
			return c.Respond(&telebot.CallbackResponse{Text: "Error: Product ID missing.", ShowAlert: true})
		}
		productID := parts[1]
		err = h.showProductDetails(c, productID)
	case "qty": // "qty:<action>:<productID>"
		// handleQuantity parses the full callback data itself.
		err = h.handleQuantity(c)
	case "page": // Specifically for product pagination "page:cat:<categoryID>:<page>"
		if len(parts) == 4 && parts[1] == "cat" {
			categoryID := parts[2]
			// showProducts itself will parse the page number from the full callback data.
			err = h.showProducts(c, categoryID)
		} else {
			log.Printf("Invalid 'page' callback format for ProductHandler: %s", callbackData)
			return c.Respond(&telebot.CallbackResponse{Text: "Error: Invalid page data.", ShowAlert: true})
		}
	default:
		log.Printf("Unknown callback prefix for ProductHandler: %s (Full data: %s)", prefix, callbackData)
		return c.Respond(&telebot.CallbackResponse{
			Text:      "Unknown product action.",
			ShowAlert: true,
		})
	}

	if err != nil {
		log.Printf("Error processing product callback action '%s': %v (CallbackData: %s)", prefix, err, callbackData)
		// Assuming sub-handlers (showProducts, showProductDetails, handleQuantity)
		// already called c.Respond() or c.Edit() on their specific errors.
		// If they didn't, a generic c.Respond with error might be needed here.
	}
	return nil // Errors handled by responding/editing or logging.
}

// showProducts displays products in a category
func (h *ProductHandler) showProducts(c telebot.Context, categoryID string) error {
	products := h.shopService.GetProductsByCategory(categoryID)
	if len(products) == 0 {
		return c.Edit(
			"No products available in this category\\.",
			&telebot.ReplyMarkup{InlineKeyboard: [][]telebot.InlineButton{
				{{Text: "⬅️ Back to Categories", Data: "shop"}}, // "shop" should lead to main shop/categories view
			}},
			telebot.ModeMarkdownV2,
		)
	}

	currentPage := 0
	if c.Callback() != nil {
		parts := strings.Split(c.Callback().Data, ":")
		// Expected callback for product page: "page:cat:<categoryID>:<page>"
		if len(parts) == 4 && parts[0] == "page" && parts[1] == "cat" {
			pageNum, convErr := strconv.Atoi(parts[3])
			if convErr == nil {
				currentPage = pageNum
			} else {
				log.Printf("Error converting page number from callback: %s, error: %v", parts[3], convErr)
			}
		}
	}

	totalPages := (len(products) + productsPerPage - 1) / productsPerPage
	startIdx := currentPage * productsPerPage
	endIdx := utils.Min(startIdx+productsPerPage, len(products))

	var msg strings.Builder
	msg.WriteString(fmt.Sprintf("*Products in Category*\n\n"))
	msg.WriteString("```\n")

	maxNameLen := 0
	for _, prod := range products[startIdx:endIdx] {
		if len(prod.Name) > maxNameLen {
			maxNameLen = len(prod.Name)
		}
	}

	headerFormat := fmt.Sprintf("%%-%ds  %%8s  %%8s\n", maxNameLen)
	msg.WriteString(fmt.Sprintf(headerFormat, "Product", "Price", "Stock"))
	msg.WriteString(strings.Repeat("-", maxNameLen+20) + "\n")

	itemFormat := fmt.Sprintf("%%-%ds  %%8.2f  %%8d\n", maxNameLen)
	for _, product := range products[startIdx:endIdx] {
		msg.WriteString(fmt.Sprintf(itemFormat,
			product.Name,
			product.Price,
			product.StockQty,
		))
	}
	msg.WriteString("```\n")
	msg.WriteString("\nSelect a product to view details:")

	var buttons [][]telebot.InlineButton
	var row []telebot.InlineButton
	for _, product := range products[startIdx:endIdx] {
		button := telebot.InlineButton{
			Text: fmt.Sprintf("📦 %s", product.Name),
			Data: fmt.Sprintf("product:%s", product.ID),
		}
		row = append(row, button)
		if len(row) == 2 {
			buttons = append(buttons, row)
			row = nil
		}
	}
	if len(row) > 0 {
		buttons = append(buttons, row)
	}

	if totalPages > 1 {
		var navRow []telebot.InlineButton
		if currentPage > 0 {
			navRow = append(navRow, telebot.InlineButton{
				Text: "⬅️ Previous",
				Data: fmt.Sprintf("page:cat:%s:%d", categoryID, currentPage-1),
			})
		}
		navRow = append(navRow, telebot.InlineButton{
			Text: fmt.Sprintf("📄 %d/%d", currentPage+1, totalPages),
			Data: "noop",
		})
		if currentPage < totalPages-1 {
			navRow = append(navRow, telebot.InlineButton{
				Text: "Next ➡️",
				Data: fmt.Sprintf("page:cat:%s:%d", categoryID, currentPage+1),
			})
		}
		buttons = append(buttons, navRow)
	}

	var actionRow []telebot.InlineButton
	actionRow = append(actionRow, telebot.InlineButton{
		Text: "⬅️ Back to Categories",
		Data: "shop",
	})
	actionRow = append(actionRow, telebot.InlineButton{
		Text: "🛒 View Cart",
		Data: "cart:show",
	})
	buttons = append(buttons, actionRow)

	return c.Edit(
		msg.String(),
		&telebot.ReplyMarkup{InlineKeyboard: buttons},
		telebot.ModeMarkdownV2,
	)
}

// showProductDetails shows product details and quantity selector
func (h *ProductHandler) showProductDetails(c telebot.Context, productID string) error {
	product := h.shopService.GetProduct(productID)
	if product == nil {
		return c.Edit("Product not found\\.")
	}

	session := h.sessions.GetSession(c.Sender().ID)
	if session.SelectedItems == nil {
		session.SelectedItems = make(map[string]interface{})
	}

	qty := 1
	qtyKey := fmt.Sprintf("qty_%s", productID)
	if v, ok := session.SelectedItems[qtyKey]; ok { // Check for product-specific quantity first
		if qtyFloat, ok := v.(float64); ok {
			qty = int(qtyFloat)
		} else if qtyInt, ok := v.(int); ok {
			qty = qtyInt
		}
	} else if v, ok := session.SelectedItems["qty"]; ok { // Fallback to generic "qty"
		// This part might be removed if qty is always product-specific
		if qtyFloat, ok := v.(float64); ok {
			qty = int(qtyFloat)
		} else if qtyInt, ok := v.(int); ok {
			qty = qtyInt
		}
	}
	// Ensure the session reflects the quantity for the current product
	session.SelectedItems[qtyKey] = qty
	if session.SelectedItems["product_id_for_qty"] != productID {
		session.SelectedItems["qty"] = qty // Update generic qty if this is a new product context
		session.SelectedItems["product_id_for_qty"] = productID
	}

	var msg strings.Builder
	msg.WriteString(fmt.Sprintf("*%s*\n\n", utils.EscapeMarkdownV2(product.Name)))
	msg.WriteString("```\n")
	msg.WriteString(fmt.Sprintf("Price: %.2f KES\n", product.Price))
	msg.WriteString(fmt.Sprintf("Stock: %d\n", product.StockQty))
	msg.WriteString(fmt.Sprintf("Selected Quantity: %d\n", qty))
	msg.WriteString(fmt.Sprintf("Subtotal: %.2f KES\n", float64(qty)*product.Price))
	msg.WriteString("```\n")

	var buttons [][]telebot.InlineButton
	buttons = append(buttons, []telebot.InlineButton{
		{Text: "➖", Data: fmt.Sprintf("qty:dec:%s", productID)},
		{Text: fmt.Sprintf("%d", qty), Data: "noop"},
		{Text: "➕", Data: fmt.Sprintf("qty:inc:%s", productID)},
	})
	buttons = append(buttons, []telebot.InlineButton{
		{Text: "🛒 Add to Cart", Data: fmt.Sprintf("cart:add:%s:%d", productID, qty)},
		{Text: "👀 View Cart", Data: "cart:show"},
	})
	buttons = append(buttons, []telebot.InlineButton{
		{Text: "⬅️ Back", Data: fmt.Sprintf("cat:%s", product.CategoryID)},
	})

	return c.Edit(
		msg.String(),
		&telebot.ReplyMarkup{InlineKeyboard: buttons},
		telebot.ModeMarkdownV2,
	)
}

// handleQuantity handles quantity adjustment for a product
func (h *ProductHandler) handleQuantity(c telebot.Context) error {
	parts := strings.Split(c.Callback().Data, ":")
	if len(parts) < 3 {
		return fmt.Errorf("invalid quantity callback data: %s", c.Callback().Data)
	}

	action := parts[1]
	productID := parts[2]

	session := h.sessions.GetSession(c.Sender().ID)
	if session.SelectedItems == nil {
		session.SelectedItems = make(map[string]interface{})
	}

	qty := 1
	qtyKey := fmt.Sprintf("qty_%s", productID)
	if v, ok := session.SelectedItems[qtyKey]; ok {
		if qtyFloat, ok := v.(float64); ok {
			qty = int(qtyFloat)
		} else if qtyInt, ok := v.(int); ok {
			qty = qtyInt
		}
	}

	product := h.shopService.GetProduct(productID)
	if product == nil {
		return c.Respond(&telebot.CallbackResponse{
			Text:      "Product not found for quantity adjustment.",
			ShowAlert: true,
		})
	}

	switch action {
	case "inc":
		if qty < product.StockQty {
			qty++
		} else {
			return c.Respond(&telebot.CallbackResponse{
				Text:      "Cannot exceed available stock.",
				ShowAlert: true,
			})
		}
	case "dec":
		if qty > 1 {
			qty--
		}
	default:
		return fmt.Errorf("unknown quantity action: %s", action)
	}

	session.SelectedItems[qtyKey] = qty
	session.SelectedItems["qty"] = qty
	session.SelectedItems["product_id_for_qty"] = productID

	if err := h.sessions.SaveSession(session); err != nil {
		log.Printf("Error saving session in handleQuantity: %v", err)
		return c.Respond(&telebot.CallbackResponse{
			Text:      "Failed to update quantity in session.",
			ShowAlert: true,
		})
	}

	return h.showProductDetails(c, productID)
}

// Constants that were in shop.go, needed by showProducts
const (
	productsPerPage = 5
)
