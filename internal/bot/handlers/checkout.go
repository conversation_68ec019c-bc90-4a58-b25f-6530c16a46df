package handlers

import (
	"context"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/itunza/telegram-silcore/internal/services"
	"github.com/itunza/telegram-silcore/internal/types"
	"gopkg.in/telebot.v3"
)

type CheckoutHandler struct {
	shopService    *services.ShopService
	sessions       *services.SessionService
	authService    *services.AuthService
	orderService   *services.OrderService
	paymentService *services.PaymentService
}

func NewCheckoutHandler(
	shopService *services.ShopService,
	sessions *services.SessionService,
	authService *services.AuthService,
	orderService *services.OrderService,
	paymentService *services.PaymentService,
) *CheckoutHandler {
	return &CheckoutHandler{
		shopService:    shopService,
		sessions:       sessions,
		authService:    authService,
		orderService:   orderService,
		paymentService: paymentService,
	}
}

func (h *CheckoutHandler) RegisterHandlers(bot *telebot.Bot) {
	bot.Handle("/checkout", h.HandleCheckout)
}

func (h *CheckoutHandler) HandleCheckout(c telebot.Context) error {
	if err := h.validateSession(c); err != nil {
		return c.Send("⚠️ Please login first to checkout.")
	}

	session := h.sessions.GetSession(c.Sender().ID)
	customer := session.GetActiveCustomer()

	if len(customer.Cart) == 0 {
		return c.Send("Your cart is empty. Add some items first!")
	}

	// Start checkout flow
	return h.showCheckoutSummary(c)
}

func (h *CheckoutHandler) showCheckoutSummary(c telebot.Context) error {
	session := h.sessions.GetSession(c.Sender().ID)
	customer := session.GetActiveCustomer()

	summary, total, err := h.orderService.GenerateOrderSummary(customer.Cart)
	if err != nil {
		return fmt.Errorf("failed to generate order summary: %w", err)
	}

	msg := &strings.Builder{}
	msg.WriteString("*Order Summary*\n\n")
	msg.WriteString(summary)
	msg.WriteString(fmt.Sprintf("\n*Total: %.2f*", total))
	msg.WriteString("\n\nSelect payment method:")

	// Create payment method selection buttons
	buttons := [][]telebot.InlineButton{
		{{Text: "💳 M-Pesa", Data: "checkout:payment:mpesa"}},
		{{Text: "📊 On Account", Data: "checkout:payment:account"}},
		{{Text: "🔙 Back to Cart", Data: "checkout:back"}},
	}

	return c.Edit(
		msg.String(),
		&telebot.ReplyMarkup{InlineKeyboard: buttons},
		telebot.ModeMarkdownV2,
	)
}

func (h *CheckoutHandler) HandleCallback(c telebot.Context) error {
	data := c.Callback().Data
	parts := strings.Split(data, ":")

	if len(parts) < 2 || parts[0] != "checkout" {
		return nil
	}

	// Get or initialize session and customer data
	session := h.sessions.GetSession(c.Sender().ID)
	if session == nil {
		return fmt.Errorf("no active session found")
	}

	customer := session.GetActiveCustomer()
	if customer == nil {
		// Initialize a new customer session if none exists
		customer = &services.CustomerSession{
			CustomerID:   "WALK-IN",
			CustomerCode: "WALK-IN", // Set default customer code
			Cart:         make(map[string]*services.CartItem),
			CreatedAt:    time.Now(),
			LastActive:   time.Now(),
		}
		// Save the new customer in session
		if session.Customers == nil {
			session.Customers = make(map[string]*services.CustomerSession)
		}
		session.Customers["WALK-IN"] = customer
		session.ActiveCustomerID = "WALK-IN"

		if err := h.sessions.SaveSession(session); err != nil {
			return fmt.Errorf("failed to save session: %w", err)
		}
	}

	switch parts[1] {
	case "payment":
		if len(parts) < 3 {
			return nil
		}
		return h.handlePaymentMethod(c, parts[2])
	case "confirm":
		return h.handleOrderConfirmation(c)
	case "back":
		return h.handleBackToCart(c)
	}

	return nil
}

func (h *CheckoutHandler) handlePaymentMethod(c telebot.Context, method string) error {
	session := h.sessions.GetSession(c.Sender().ID)
	customerSession := session.GetActiveCustomer()

	// Convert cart items from services.CartItem to types.CartItem
	convertedCart := make(map[string]*types.CartItem)
	for k, v := range customerSession.Cart {
		convertedCart[k] = &types.CartItem{
			ProductID: v.ProductID,
			Price:     v.Price,
			Quantity:  v.Quantity,
			Warehouse: v.Warehouse,
		}
	}

	// Show processing message and capture the message for later editing
	processingMsg, err := c.Bot().Send(c.Recipient(), "Processing your payment request...")
	if err != nil {
		log.Printf("Error sending processing message: %v", err)
	}

	// Convert CustomerSession to types.Customer
	customer := &types.Customer{
		ID:           customerSession.CustomerID,
		CustomerCode: "WALK-IN", // Set default for M-Pesa payments
		Cart:         convertedCart,
	}

	// Save payment state in session
	session.CurrentMenu = "payment"
	session.PaymentState = &services.PaymentState{
		Method: method,
		Status: "pending",
	}

	// Calculate total amount
	var total float64
	for _, item := range customerSession.Cart {
		total += item.Price * float64(item.Quantity)
	}
	session.PaymentState.Amount = total

	if err := h.sessions.SaveSession(session); err != nil {
		return fmt.Errorf("failed to save payment state: %w", err)
	}

	switch method {
	case "mpesa":
		// Create payment context
		paymentCtx := &types.PaymentContext{
			UserID:    c.Sender().ID,
			ChatID:    c.Chat().ID,
			MessageID: processingMsg.ID,
			Customer:  customer,
			PaymentID: session.PaymentState.ID,
		}

		// Initiate M-Pesa payment with the new context
		if err := h.paymentService.InitiateMPesaPayment(context.Background(), paymentCtx); err != nil {
			log.Printf("Error initiating M-Pesa payment: %v", err)
			return c.Edit(
				"⚠️ Failed to initiate M-Pesa payment. Please try again later.",
				&telebot.ReplyMarkup{
					InlineKeyboard: [][]telebot.InlineButton{
						{{Text: "🔙 Back to Cart", Data: "checkout:back"}},
					},
				},
			)
		}
		return nil

	case "account":
		if customer.ID == "WALK-IN" {
			return c.Edit("Account payment is only available for registered customers.")
		}
		return c.Send("Account payment is not ready yet")
		
	default:
		return fmt.Errorf("invalid payment method: %s", method)
	}
}

func (h *CheckoutHandler) handleOrderConfirmation(c telebot.Context) error {
	session := h.sessions.GetSession(c.Sender().ID)
	customer := session.GetActiveCustomer()

	orderRefs, err := h.orderService.CreateOrders(c.Sender().ID, customer.Cart)
	if err != nil {
		return fmt.Errorf("failed to create orders: %w", err)
	}

	// Clear cart after successful order creation
	customer.Cart = make(map[string]*services.CartItem)
	if err := h.sessions.SaveSession(session); err != nil {
		log.Printf("Error saving session after order: %v", err)
	}

	return h.showOrderSuccess(c, orderRefs)
}

func (h *CheckoutHandler) showOrderSuccess(c telebot.Context, orderRefs []string) error {
	msg := &strings.Builder{}
	msg.WriteString("✅ *Order Successfully Created*\n\n")
	msg.WriteString("Your order references:\n")
	for _, ref := range orderRefs {
		msg.WriteString(fmt.Sprintf("• `%s`\n", ref))
	}
	msg.WriteString("\nThank you for your purchase!")

	buttons := [][]telebot.InlineButton{
		{{Text: "🏪 Continue Shopping", Data: "shop:browse"}},
		{{Text: "📦 View Orders", Data: "orders:list"}},
	}

	return c.Edit(
		msg.String(),
		&telebot.ReplyMarkup{InlineKeyboard: buttons},
		telebot.ModeMarkdownV2,
	)
}

func (h *CheckoutHandler) handleBackToCart(c telebot.Context) error {
	shopHandler := NewShopHandler(h.shopService, h.sessions, h.authService, h.paymentService)
	return shopHandler.showCart(c)
}

func (h *CheckoutHandler) validateSession(c telebot.Context) error {
	if !h.authService.ValidateSession(c.Sender().ID) {
		return fmt.Errorf("invalid session")
	}
	return nil
}
