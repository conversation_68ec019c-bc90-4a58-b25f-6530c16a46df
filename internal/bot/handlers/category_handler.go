package handlers

import (
	"fmt"
	"log"
	"strconv"
	"strings"

	"github.com/itunza/telegram-silcore/internal/bot/utils"
	"github.com/itunza/telegram-silcore/internal/services"
	telebot "gopkg.in/telebot.v3"
)

// CategoryHandler handles operations related to displaying product categories.
type CategoryHandler struct {
	shopService *services.ShopService
	sessions    *services.SessionService
}

// NewCategoryHandler creates a new CategoryHandler instance.
func NewCategoryHandler(shopService *services.ShopService, sessions *services.SessionService) *CategoryHandler {
	return &CategoryHandler{
		shopService: shopService,
		sessions:    sessions,
	}
}

const (
	categoriesPerPage = 6 // Number of categories to show per page
)

// HandleCategoryCallback handles callbacks related to category display, primarily pagination.
// Expected callback data: "catpage:<page_number>"
func (h *CategoryHandler) HandleCategoryCallback(c telebot.Context) error {
	callbackData := c.Callback().Data
	parts := strings.Split(callbackData, ":")

	// Expecting "catpage:X"
	if len(parts) == 2 && parts[0] == "catpage" {
		// pageNumberStr := parts[1]
		// Page number is parsed by showCategories itself based on the callback data.
		return h.showCategories(c)
	}

	log.Printf("Unknown or invalid callback data for CategoryHandler: %s", callbackData)
	return c.Respond(&telebot.CallbackResponse{
		Text:      "Invalid category action.",
		ShowAlert: true,
	})
}

// showCategories displays available product categories
func (h *CategoryHandler) showCategories(c telebot.Context) error {
	categories := h.shopService.GetCategories()
	if len(categories) == 0 {
		// Use c.Edit or c.Send based on context. For simplicity, assume c.Edit is desired for callbacks.
		// If c.Callback() is nil, it's likely an initial command, so c.Send would be appropriate.
		// This logic can be refined in the calling handler (e.g. ShopHandler.Handle).
		if c.Callback() == nil {
			return c.Send(
				"No categories available\\.",
				&telebot.ReplyMarkup{InlineKeyboard: [][]telebot.InlineButton{
					// "shop" callback data should ideally re-trigger the initial category view (page 0)
					// This can be handled by ShopHandler's "shop" case or by making it "catpage:0"
					{{Text: "🔄 Refresh", Data: "shop"}},
				}},
				telebot.ModeMarkdownV2,
			)
		}
		return c.Edit(
			"No categories available\\.",
			&telebot.ReplyMarkup{InlineKeyboard: [][]telebot.InlineButton{
				// "shop" callback data should ideally re-trigger the initial category view (page 0)
				// This can be handled by ShopHandler's "shop" case or by making it "catpage:0"
				{{Text: "🔄 Refresh", Data: "shop"}},
			}},
			telebot.ModeMarkdownV2,
		)
	}

	currentPage := 0
	if c.Callback() != nil {
		parts := strings.Split(c.Callback().Data, ":")
		// Expects "catpage:<pageNo>" for category pagination
		// Or "shop" for initial view (which implies page 0)
		if len(parts) == 2 && (parts[0] == "catpage" || parts[0] == "page") { // Accommodate old "page:X" and new "catpage:X"
			pageNo, err := strconv.Atoi(parts[1])
			if err == nil {
				currentPage = pageNo
			} else {
				log.Printf("Error converting page number for categories: %v from data: %s", err, c.Callback().Data)
			}
		} else if parts[0] == "shop" { // "shop" callback implies first page
			currentPage = 0
		}
	}

	totalPages := (len(categories) + categoriesPerPage - 1) / categoriesPerPage
	startIdx := currentPage * categoriesPerPage
	endIdx := utils.Min(startIdx+categoriesPerPage, len(categories))

	var msg strings.Builder
	msg.WriteString("*Welcome to Our Shop* 🛍️\n\n")
	msg.WriteString("Available Categories:\n")
	msg.WriteString("```\n")

	maxNameLen := 0
	for _, cat := range categories[startIdx:endIdx] {
		if len(cat.Name) > maxNameLen {
			maxNameLen = len(cat.Name)
		}
	}

	headerFormat := fmt.Sprintf("%%-%ds  %%8s\n", maxNameLen)
	msg.WriteString(fmt.Sprintf(headerFormat, "Category", "Items"))
	msg.WriteString(strings.Repeat("-", maxNameLen+10) + "\n")

	itemFormat := fmt.Sprintf("%%-%ds  %%8d\n", maxNameLen)
	for _, cat := range categories[startIdx:endIdx] {
		productCount := len(h.shopService.GetProductsByCategory(cat.ID))
		msg.WriteString(fmt.Sprintf(itemFormat, cat.Name, productCount))
	}
	msg.WriteString("```\n")

	var buttons [][]telebot.InlineButton
	var row []telebot.InlineButton
	for _, category := range categories[startIdx:endIdx] {
		button := telebot.InlineButton{
			Text: fmt.Sprintf("📁 %s", utils.EscapeMarkdownV2(category.Name)),
			Data: fmt.Sprintf("cat:%s", category.ID),
		}
		row = append(row, button)
		if len(row) == 2 {
			buttons = append(buttons, row)
			row = nil
		}
	}
	if len(row) > 0 {
		buttons = append(buttons, row)
	}

	if totalPages > 1 {
		var navRow []telebot.InlineButton
		if currentPage > 0 {
			navRow = append(navRow, telebot.InlineButton{
				Text: "⬅️ Previous",
				Data: fmt.Sprintf("catpage:%d", currentPage-1), // Use "catpage" for category pagination
			})
		}
		navRow = append(navRow, telebot.InlineButton{
			Text: fmt.Sprintf("📄 %d/%d", currentPage+1, totalPages),
			Data: "noop",
		})
		if currentPage < totalPages-1 {
			navRow = append(navRow, telebot.InlineButton{
				Text: "Next ➡️",
				Data: fmt.Sprintf("catpage:%d", currentPage+1), // Use "catpage" for category pagination
			})
		}
		buttons = append(buttons, navRow)
	}

	buttons = append(buttons, []telebot.InlineButton{
		{Text: "🛒 View Cart", Data: "cart:show"},
	})

	if c.Callback() != nil {
		return c.Edit(
			msg.String(),
			&telebot.ReplyMarkup{InlineKeyboard: buttons},
			telebot.ModeMarkdownV2,
		)
	}
	// If not a callback (e.g. initial /shop command), send a new message.
	return c.Send(
		msg.String(),
		&telebot.ReplyMarkup{InlineKeyboard: buttons},
		telebot.ModeMarkdownV2,
	)
}
