package utils

import "strings"

// EscapeMarkdownV2 escapes special characters for MarkdownV2
func EscapeMarkdownV2(text string) string {
	specialChars := []string{"_", "*", "[", "]", "(", ")", "~", "`", ">", "#", "+", "-", "=", "|", "{", "}", ".", "!"}
	escaped := text
	for _, char := range specialChars {
		escaped = strings.ReplaceAll(escaped, char, "\\"+char)
	}
	return escaped
}

// Min returns the smaller of x or y.
func Min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
