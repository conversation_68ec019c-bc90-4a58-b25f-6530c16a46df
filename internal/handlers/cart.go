package handlers

// import (
// 	"fmt"

// 	"github.com/itunza/telegram-silcore/internal/services"
// 	telebot "gopkg.in/telebot.v3"
// )

// func HandleCartButton(c telebot.Context, sessionService *services.SessionService, shopService *services.ShopService) error {
// 	userID := c.Sender().ID

// 	// Get user's warehouse first
// 	warehouse, err := shopService.GetUserWarehouse(userID)
// 	if err != nil {
// 		return fmt.Errorf("failed to get warehouse: %w", err)
// 	}

// 	if warehouse == "" {
// 		return c.Send(
// 			"Please select a warehouse first using /warehouse command",
// 			&telebot.ReplyMarkup{InlineKeyboard: [][]telebot.InlineButton{
// 				{{Text: "🏭 Select Warehouse", Data: "warehouse"}},
// 			}},
// 		)
// 	}

// 	session := sessionService.GetSession(userID)
// 	cart := session.GetCart()
// 	if len(cart) == 0 {
// 		return c.Send("🛒 Your cart is empty!")
// 	}

// 	msg := "🛒 *Your Cart:*\n"
// 	total := 0.0
// 	for productID, cartItem := range cart {
// 		product := shopService.GetProduct(productID, warehouse)
// 		if product == nil {
// 			continue
// 		}
// 		qty := cartItem.Quantity
// 		msg += fmt.Sprintf("- %s × %d (%.2f KES)\n", product.Name, qty, product.Price*float64(qty))
// 		total += product.Price * float64(qty)
// 	}
// 	msg += fmt.Sprintf("\n*Total: %.2f KES*", total)

// 	return c.Send(
// 		msg,
// 		&telebot.SendOptions{ParseMode: telebot.ModeMarkdown},
// 		&telebot.ReplyMarkup{
// 			InlineKeyboard: [][]telebot.InlineButton{{
// 				{Text: "💳 Checkout Now", Data: "checkout"},
// 			}},
// 		},
// 	)
// }
