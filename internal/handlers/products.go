package handlers

// import (
// 	"fmt"

// 	"github.com/itunza/telegram-silcore/internal/services"
// 	telebot "gopkg.in/telebot.v3"
// )

// func HandleProducts(c telebot.Context, shopService *services.ShopService) error {
// 	// Get user's warehouse
// 	warehouse, err := shopService.GetUserWarehouse(c.Sender().ID)
// 	if err != nil || warehouse == "" {
// 		return c.Send(
// 			"Please select a warehouse first using /warehouse command",
// 			&telebot.ReplyMarkup{InlineKeyboard: [][]telebot.InlineButton{
// 				{{Text: "🏭 Select Warehouse", Data: "warehouse"}},
// 			}},
// 		)
// 	}

// 	// Get products for the default category and user's warehouse
// 	products := shopService.GetProductsByCategory("default", warehouse)

// 	var productButtons [][]telebot.InlineButton
// 	for _, p := range products {
// 		productButtons = append(productButtons, []telebot.InlineButton{{
// 			Text: fmt.Sprintf("%s - %.2f KES", p.Name, p.Price),
// 			Data: fmt.Sprintf("product_%s", p.ID),
// 		}})
// 	}

// 	// Add cart button
// 	productButtons = append(productButtons, []telebot.InlineButton{{
// 		Text: "🛒 View Cart",
// 		Data: "cart",
// 	}})

// 	return c.Send("Select a product:", &telebot.ReplyMarkup{
// 		InlineKeyboard: productButtons,
// 	})
// }
