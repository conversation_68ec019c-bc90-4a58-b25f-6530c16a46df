package services

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"regexp"
	"time"

	"github.com/itunza/telegram-silcore/internal/types"
	"github.com/redis/go-redis/v9"
)

// Add this variable to store a reference to the payment service
var paymentServiceRef *PaymentService

// SetPaymentService sets the payment service reference for the MPesa client
func SetPaymentService(service *PaymentService) {
	paymentServiceRef = service
}

type MPesaClient struct {
	config     types.MPesaConfig
	httpClient *http.Client
	baseURL    string
}

type STKPushResponse struct {
	MerchantRequestID   string `json:"MerchantRequestID"`
	CheckoutRequestID   string `json:"CheckoutRequestID"`
	ResponseCode        string `json:"ResponseCode"`
	ResponseDescription string `json:"ResponseDescription"`
	CustomerMessage     string `json:"CustomerMessage"`
}

func NewMPesaClient(config types.MPesaConfig) *MPesaClient {
	baseURL := "https://api.safaricom.co.ke"
	if config.Environment == "sandbox" {
		baseURL = "https://sandbox.safaricom.co.ke"
	}

	return &MPesaClient{
		config: config,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		baseURL: baseURL,
	}
}

func (m *MPesaClient) getAccessToken() (string, error) {
	auth := base64.StdEncoding.EncodeToString([]byte(m.config.ConsumerKey + ":" + m.config.ConsumerSecret))

	req, err := http.NewRequest("GET", m.baseURL+"/oauth/v1/generate?grant_type=client_credentials", nil)
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Add("Authorization", "Basic "+auth)

	// Log the request (without sensitive data)
	log.Printf("Requesting M-Pesa access token from: %s", m.baseURL)

	resp, err := m.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to get access token: %w", err)
	}
	defer resp.Body.Close()

	// Check status code before attempting to decode
	if resp.StatusCode != http.StatusOK {
		// Read the response body for error details
		bodyBytes, readErr := io.ReadAll(resp.Body)
		if readErr != nil {
			return "", fmt.Errorf("failed to get access token: HTTP %d and could not read response body: %v",
				resp.StatusCode, readErr)
		}

		// If body is empty, provide a more helpful error
		if len(bodyBytes) == 0 {
			return "", fmt.Errorf("failed to get access token: HTTP %d with empty response body. Check your M-Pesa credentials",
				resp.StatusCode)
		}

		return "", fmt.Errorf("failed to get access token: HTTP %d: %s",
			resp.StatusCode, string(bodyBytes))
	}

	var result struct {
		AccessToken string `json:"access_token"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return "", fmt.Errorf("failed to decode access token response: %w", err)
	}

	if result.AccessToken == "" {
		return "", fmt.Errorf("received empty access token from M-Pesa API")
	}

	log.Printf("Successfully obtained M-Pesa access token")
	return result.AccessToken, nil
}

func (m *MPesaClient) InitiateSTKPush(phoneNumber string, amount float64, paymentID string, description string) (*STKPushResponse, error) {
	token, err := m.getAccessToken()
	if err != nil {
		return nil, err
	}

	log.Printf("Initiating STK push for phone: %s, amount: %.2f, paymentID: %s", phoneNumber, amount, paymentID)

	// Ensure description meets M-Pesa requirements (alphanumeric, max 13 chars)
	if len(description) > 13 {
		description = description[:13]
	}
	// Remove any special characters
	reg := regexp.MustCompile("[^a-zA-Z0-9 ]")
	description = reg.ReplaceAllString(description, "")

	timestamp := time.Now().Format("20060102150405")
	password := base64.StdEncoding.EncodeToString([]byte(m.config.BusinessCode + m.config.PassKey + timestamp))

	payload := map[string]interface{}{
		"BusinessShortCode": m.config.BusinessCode,
		"Password":          password,
		"Timestamp":         timestamp,
		"TransactionType":   "CustomerPayBillOnline",
		"Amount":            amount,
		"PartyA":            phoneNumber,
		"PartyB":            m.config.BusinessCode,
		"PhoneNumber":       phoneNumber,
		"CallBackURL":       m.config.CallbackURL,
		"AccountReference":  paymentID,
		"TransactionDesc":   description,
	}

	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal payload: %w", err)
	}

	req, err := http.NewRequest("POST", m.baseURL+"/mpesa/stkpush/v1/processrequest", bytes.NewBuffer(payloadBytes))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Add("Authorization", "Bearer "+token)
	req.Header.Add("Content-Type", "application/json")

	resp, err := m.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// Read the response body
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// Log the response status and body
	log.Printf("STK push response body: %s", string(bodyBytes))

	// Check for non-200 status codes
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("STK push failed with status %d: %s", resp.StatusCode, string(bodyBytes))
	}

	// Create a new reader from the bytes for json.Decoder
	responseBody := bytes.NewReader(bodyBytes)

	var result STKPushResponse
	if err := json.NewDecoder(responseBody).Decode(&result); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w, body: %s", err, string(bodyBytes))
	}

	// Check if the response code indicates success
	if result.ResponseCode != "0" {
		log.Printf("STK push returned non-zero response code: %s - %s",
			result.ResponseCode, result.ResponseDescription)
	}

	return &result, nil
}

// InitiateSTKPushWithRedis is similar to InitiateSTKPush but also stores the checkout request ID in Redis
func (m *MPesaClient) InitiateSTKPushWithRedis(
	phoneNumber string,
	amount float64,
	accountRef string, // This is now the SO reference
	description string,
	redisClient *redis.Client,
	ctx context.Context,
) (*STKPushResponse, error) {
	// Call the original method to initiate the STK push
	result, err := m.InitiateSTKPush(phoneNumber, amount, accountRef, description)
	if err != nil {
		return nil, err
	}

	// Store the SO reference in Redis using the checkout request ID as key
	key := fmt.Sprintf("mpesa:checkout:%s", result.CheckoutRequestID)
	if err := redisClient.Set(ctx, key, accountRef, 15*time.Minute).Err(); err != nil {
		log.Printf("Warning: Failed to store SO reference in Redis: %v", err)
	} else {
		log.Printf("Stored SO reference %s in Redis with key %s (expires in 15 minutes)",
			accountRef, key)
	}

	return result, nil
}

// CheckTransactionStatus checks the status of a transaction from the database
func (m *MPesaClient) CheckTransactionStatus(ctx context.Context, checkoutRequestID string, paymentID string, redisClient *redis.Client) error {
	log.Printf("Checking payment status from database for checkout request ID: %s, payment ID: %s", checkoutRequestID, paymentID)

	// Get the payment service
	paymentService := paymentServiceRef
	if paymentService == nil {
		return fmt.Errorf("payment service not initialized")
	}

	// Get payment from database
	payment, err := paymentService.GetPayment(ctx, paymentID)
	if err != nil {
		return fmt.Errorf("failed to get payment from database: %w", err)
	}

	// If payment is already completed or failed, clean up Redis key and return
	if payment.Status == "completed" || payment.Status == "failed" {
		key := fmt.Sprintf("mpesa:checkout:%s", checkoutRequestID)
		if err := redisClient.Del(ctx, key).Err(); err != nil {
			log.Printf("Warning: Failed to delete Redis key %s: %v", key, err)
		}
		log.Printf("Payment %s already in final state: %s", paymentID, payment.Status)

		// If payment is completed, ensure warehouse is set before creating order
		if payment.Status == "completed" {
			// Get user's assigned warehouse from Redis or default
			warehouseKey := fmt.Sprintf("user:warehouse:%d", payment.UserID)
			warehouse, err := redisClient.Get(ctx, warehouseKey).Result()
			if err != nil || warehouse == "" {
				warehouse = "Main Warehouse" // Default warehouse if none assigned
			}

			// Update payment with warehouse information
			if err := paymentService.UpdatePaymentWarehouse(ctx, paymentID, warehouse); err != nil {
				log.Printf("Warning: Failed to update payment warehouse: %v", err)
			}
		}
		return nil
	}

	// If payment is still pending, create a failed callback after timeout
	callback := &MPesaCallback{
		Body: struct {
			StkCallback struct {
				MerchantRequestID string `json:"MerchantRequestID"`
				CheckoutRequestID string `json:"CheckoutRequestID"`
				ResultCode        int    `json:"ResultCode"`
				ResultDesc        string `json:"ResultDesc"`
				CallbackMetadata  struct {
					Item []struct {
						Name  string      `json:"Name"`
						Value interface{} `json:"Value"`
					} `json:"Item"`
				} `json:"CallbackMetadata"`
			} `json:"stkCallback"`
		}{
			StkCallback: struct {
				MerchantRequestID string `json:"MerchantRequestID"`
				CheckoutRequestID string `json:"CheckoutRequestID"`
				ResultCode        int    `json:"ResultCode"`
				ResultDesc        string `json:"ResultDesc"`
				CallbackMetadata  struct {
					Item []struct {
						Name  string      `json:"Name"`
						Value interface{} `json:"Value"`
					} `json:"Item"`
				} `json:"CallbackMetadata"`
			}{
				CheckoutRequestID: checkoutRequestID,
				ResultCode:        1, // Failed
				ResultDesc:        "Payment timeout - No response received",
				CallbackMetadata: struct {
					Item []struct {
						Name  string      `json:"Name"`
						Value interface{} `json:"Value"`
					} `json:"Item"`
				}{},
			},
		},
	}

	// Process the callback to mark payment as failed
	if err := paymentService.HandleMPesaCallback(ctx, callback); err != nil {
		return fmt.Errorf("failed to process timeout callback: %w", err)
	}

	// Clean up Redis key
	key := fmt.Sprintf("mpesa:checkout:%s", checkoutRequestID)
	if err := redisClient.Del(ctx, key).Err(); err != nil {
		log.Printf("Warning: Failed to delete Redis key %s: %v", key, err)
	}

	log.Printf("Marked payment %s as failed due to timeout", paymentID)
	return nil
}

// ScheduleTransactionStatusCheck schedules a check for transaction status after a delay
func (m *MPesaClient) ScheduleTransactionStatusCheck(ctx context.Context, checkoutRequestID string, paymentID string, redisClient *redis.Client) {
	bgCtx := context.Background()
	log.Printf("Created background context for transaction status check")

	go func() {
		log.Printf("Scheduled payment cancellation check for checkout request ID: %s in 30 seconds", checkoutRequestID)
		time.Sleep(30 * time.Second)

		key := fmt.Sprintf("mpesa:checkout:%s", checkoutRequestID)
		exists, err := redisClient.Exists(bgCtx, key).Result()
		if err != nil {
			log.Printf("Error checking if Redis key exists: %v", err)
			return
		}

		if exists == 0 {
			log.Printf("Redis key %s no longer exists, webhook was processed already", key)
			return
		}

		log.Printf("No webhook received after 30 seconds, marking payment as failed")

		// Get the payment to retrieve user ID
		paymentService := paymentServiceRef
		if paymentService == nil {
			log.Printf("Payment service not initialized, cannot process failed transaction")
			return
		}

		payment, err := paymentService.GetPayment(bgCtx, paymentID)
		if err != nil {
			log.Printf("Error getting payment: %v", err)
			return
		}

		// Get user's warehouse from Redis
		warehouseKey := fmt.Sprintf("user:warehouse:%d", payment.UserID)
		warehouse, err := redisClient.Get(bgCtx, warehouseKey).Result()
		if err != nil || warehouse == "" {
			warehouse = "Main Warehouse" // Default warehouse if none assigned
		}

		callback := &MPesaCallback{
			Body: struct {
				StkCallback struct {
					MerchantRequestID string `json:"MerchantRequestID"`
					CheckoutRequestID string `json:"CheckoutRequestID"`
					ResultCode        int    `json:"ResultCode"`
					ResultDesc        string `json:"ResultDesc"`
					CallbackMetadata  struct {
						Item []struct {
							Name  string      `json:"Name"`
							Value interface{} `json:"Value"`
						} `json:"Item"`
					} `json:"CallbackMetadata"`
				} `json:"stkCallback"`
			}{
				StkCallback: struct {
					MerchantRequestID string `json:"MerchantRequestID"`
					CheckoutRequestID string `json:"CheckoutRequestID"`
					ResultCode        int    `json:"ResultCode"`
					ResultDesc        string `json:"ResultDesc"`
					CallbackMetadata  struct {
						Item []struct {
							Name  string      `json:"Name"`
							Value interface{} `json:"Value"`
						} `json:"Item"`
					} `json:"CallbackMetadata"`
				}{
					CheckoutRequestID: checkoutRequestID,
					ResultCode:        1, // Failed
					ResultDesc:        "Payment timeout - No response received after 20 seconds",
				},
			},
		}

		// Update payment with warehouse before marking as failed
		if err := paymentService.UpdatePaymentWarehouse(bgCtx, paymentID, warehouse); err != nil {
			log.Printf("Warning: Failed to update payment warehouse: %v", err)
		}

		if err := paymentService.HandleMPesaCallback(bgCtx, callback); err != nil {
			log.Printf("Failed to process timed-out transaction: %v", err)
		}

		if err := redisClient.Del(bgCtx, key).Err(); err != nil {
			log.Printf("Warning: Failed to delete Redis key %s: %v", key, err)
		}
	}()
}

// QueryTransactionStatus checks the status of an M-Pesa transaction using the checkout request ID
func (m *MPesaClient) QueryTransactionStatus(checkoutRequestID string) (*TransactionStatusResponse, error) {
	token, err := m.getAccessToken()
	if err != nil {
		return nil, fmt.Errorf("failed to get access token: %w", err)
	}

	log.Printf("Querying transaction status for checkout request ID: %s", checkoutRequestID)

	timestamp := time.Now().Format("20060102150405")
	password := base64.StdEncoding.EncodeToString([]byte(m.config.BusinessCode + m.config.PassKey + timestamp))

	payload := map[string]interface{}{
		"BusinessShortCode": m.config.BusinessCode,
		"Password":          password,
		"Timestamp":         timestamp,
		"CheckoutRequestID": checkoutRequestID,
	}

	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal payload: %w", err)
	}

	req, err := http.NewRequest("POST", m.baseURL+"/mpesa/stkpushquery/v1/query", bytes.NewBuffer(payloadBytes))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Add("Authorization", "Bearer "+token)
	req.Header.Add("Content-Type", "application/json")

	resp, err := m.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// Read the response body
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// Log the response status and body
	log.Printf("Transaction status query response: %s", string(bodyBytes))

	// Check for non-200 status codes
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("transaction status query failed with status %d: %s", resp.StatusCode, string(bodyBytes))
	}

	// Create a new reader from the bytes for json.Decoder
	responseBody := bytes.NewReader(bodyBytes)

	var result TransactionStatusResponse
	if err := json.NewDecoder(responseBody).Decode(&result); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w, body: %s", err, string(bodyBytes))
	}

	return &result, nil
}

// TransactionStatusResponse represents the response from the M-Pesa transaction status query
type TransactionStatusResponse struct {
	MerchantRequestID   string `json:"MerchantRequestID"`
	CheckoutRequestID   string `json:"CheckoutRequestID"`
	ResponseCode        string `json:"ResponseCode"`
	ResponseDescription string `json:"ResponseDescription"`
	ResultCode          string `json:"ResultCode"`
	ResultDesc          string `json:"ResultDesc"`
}

//
