package services

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"

	"github.com/redis/go-redis/v9"
)

// WebhookService handles incoming webhooks
type WebhookService struct {
	paymentService *PaymentService
	redis          *redis.Client
}

// NewWebhookService creates a new webhook service
func NewWebhookService(paymentService *PaymentService, redisURL string) (*WebhookService, error) {
	opt, err := redis.ParseURL(redisURL)
	if err != nil {
		return nil, fmt.Errorf("failed to parse Redis URL: %w", err)
	}

	client := redis.NewClient(opt)
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	return &WebhookService{
		paymentService: paymentService,
		redis:          client,
	}, nil
}

// Close closes the Redis connection
func (s *WebhookService) Close() error {
	return s.redis.Close()
}

// StartServer starts the webhook HTTP server
func (s *WebhookService) StartServer(port string) {
	mux := http.NewServeMux()

	// M-Pesa callback endpoint
	mux.HandleFunc("/mpesa/silcore", s.handleMPesaCallback)

	// Health check endpoint
	mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	})

	server := &http.Server{
		Addr:    ":" + port,
		Handler: mux,
	}

	// log.Printf("Starting webhook server on port %s", port)
	go func() {
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Webhook server error: %v", err)
		}
	}()
}

// handleMPesaCallback processes M-Pesa callback requests
func (s *WebhookService) handleMPesaCallback(w http.ResponseWriter, r *http.Request) {
	// Only accept POST requests
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Read request body
	body, err := io.ReadAll(r.Body)
	if err != nil {
		log.Printf("Error reading request body: %v", err)
		http.Error(w, "Error reading request", http.StatusBadRequest)
		return
	}
	defer r.Body.Close()

	// Log the raw callback for debugging
	log.Printf("Received M-Pesa callback: %s", string(body))

	// Check if the body is empty
	if len(body) == 0 {
		log.Printf("Error: Empty callback received")
		http.Error(w, "Empty request body", http.StatusBadRequest)
		return
	}

	// Parse the callback
	var callback MPesaCallback
	if err := json.Unmarshal(body, &callback); err != nil {
		log.Printf("Error parsing callback JSON: %v", err)
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	// Validate the callback has the required fields
	if callback.Body.StkCallback.CheckoutRequestID == "" {
		log.Printf("Error: Missing CheckoutRequestID in callback")
		http.Error(w, "Missing required fields", http.StatusBadRequest)
		return
	}

	// Process the callback
	ctx := context.Background()
	if err := s.processCallback(ctx, &callback); err != nil {
		log.Printf("Error processing callback: %v", err)
		http.Error(w, "Processing error", http.StatusInternalServerError)
		return
	}

	// Respond with success
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]string{
		"ResultCode": "0",
		"ResultDesc": "Success",
	})
}

// MPesaCallback represents the callback data from M-Pesa
type MPesaCallback struct {
	Body struct {
		StkCallback struct {
			MerchantRequestID string `json:"MerchantRequestID"`
			CheckoutRequestID string `json:"CheckoutRequestID"`
			ResultCode        int    `json:"ResultCode"`
			ResultDesc        string `json:"ResultDesc"`
			CallbackMetadata  struct {
				Item []struct {
					Name  string      `json:"Name"`
					Value interface{} `json:"Value"`
				} `json:"Item"`
			} `json:"CallbackMetadata"`
		} `json:"stkCallback"`
	} `json:"Body"`
}

// processCallback processes the M-Pesa callback data
func (s *WebhookService) processCallback(ctx context.Context, callback *MPesaCallback) error {
	stkCallback := callback.Body.StkCallback
	checkoutRequestID := stkCallback.CheckoutRequestID
	resultCode := stkCallback.ResultCode
	resultDesc := stkCallback.ResultDesc

	// Get the payment ID from Redis
	key := fmt.Sprintf("mpesa:checkout:%s", checkoutRequestID)

	// First, check if we already have a payment record in the database with this checkout request ID
	var payment Payment
	err := s.paymentService.db.WithContext(ctx).Where("reference = ?", checkoutRequestID).First(&payment).Error
	if err == nil {
		// We found a payment with this checkout request ID
		log.Printf("Found payment ID %s for checkout request ID %s in database", payment.ID, checkoutRequestID)
	} else {
		// Try to get the payment ID from Redis
		paymentID, err := s.redis.Get(ctx, key).Result()
		if err != nil {
			if err == redis.Nil {
				return fmt.Errorf("no payment found for checkout request ID: %s", checkoutRequestID)
			}
			return fmt.Errorf("redis error: %w", err)
		}

		// Look up the payment in the database using the ID from Redis
		err = s.paymentService.db.WithContext(ctx).First(&payment, paymentID).Error
		if err != nil {
			return fmt.Errorf("payment ID %s from Redis not found in database: %w", paymentID, err)
		}
		log.Printf("Found payment ID %s for checkout request ID %s via Redis", payment.ID, checkoutRequestID)
	}

	// Process based on result code
	if resultCode == 0 {
		// Success - extract payment details
		var amount, mpesaReceiptNumber string

		if stkCallback.CallbackMetadata.Item != nil {
			for _, item := range stkCallback.CallbackMetadata.Item {
				switch item.Name {
				case "Amount":
					if val, ok := item.Value.(float64); ok {
						amount = fmt.Sprintf("%.2f", val)
					}
				case "MpesaReceiptNumber":
					if val, ok := item.Value.(string); ok {
						mpesaReceiptNumber = val
					}
				}
			}
		} else {
			log.Printf("Warning: CallbackMetadata.Item is nil for checkout request ID: %s", checkoutRequestID)
		}

		// Update payment in database
		if err := s.paymentService.CompletePayment(ctx, payment.ID, mpesaReceiptNumber, amount); err != nil {
			return fmt.Errorf("failed to complete payment: %w", err)
		}

	} else {
		// Failed payment
		if err := s.paymentService.FailPayment(ctx, payment.ID, resultDesc); err != nil {
			return fmt.Errorf("failed to mark payment as failed: %w", err)
		}
	}

	log.Printf("Processed M-Pesa callback for payment ID: %s", payment.ID)

	// Clean up Redis key
	if err := s.redis.Del(ctx, key).Err(); err != nil {
		log.Printf("Warning: Failed to delete Redis key %s: %v", key, err)
	}

	return nil
}
