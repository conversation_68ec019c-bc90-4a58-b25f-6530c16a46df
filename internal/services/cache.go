package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/itunza/telegram-silcore/internal/models"
	"github.com/redis/go-redis/v9"
)

// ProductCache manages product caching using Redis
type ProductCache struct {
	client *redis.Client
	ctx    context.Context
	ttl    time.Duration
}

// NewProductCache creates a new product cache with Redis
func NewProductCache(redisURL string, ttl time.Duration) (*ProductCache, error) {
	opt, err := redis.ParseURL(redisURL)
	if err != nil {
		return nil, fmt.Errorf("failed to parse Redis URL: %v", err)
	}

	client := redis.NewClient(opt)
	ctx := context.Background()

	// Test connection
	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %v", err)
	}

	return &ProductCache{
		client: client,
		ctx:    ctx,
		ttl:    ttl,
	}, nil
}

// productKey generates a Redis key for a product
func (c *ProductCache) productKey(productID string) string {
	return fmt.Sprintf("product:%s", productID)
}

// categoryKey generates a Redis key for a category's products
func (c *ProductCache) categoryKey(categoryID string) string {
	return fmt.Sprintf("category:%s:products", categoryID)
}

// GetProduct gets a product from cache or fetches it using the provided function
func (c *ProductCache) GetProduct(productID string, fetch func(string) *models.Product) *models.Product {
	key := c.productKey(productID)

	// Try to get from cache
	val, err := c.client.Get(c.ctx, key).Result()
	if err == nil {
		var product models.Product
		if err := json.Unmarshal([]byte(val), &product); err == nil {
			return &product
		}
	}

	// Fetch if not in cache
	product := fetch(productID)
	if product != nil {
		data, err := json.Marshal(product)
		if err == nil {
			c.client.Set(c.ctx, key, data, c.ttl)
		}
	}

	return product
}

// GetProducts gets products for a category from cache or fetches them
func (c *ProductCache) GetProducts(categoryID string, fetch func(string) []models.Product) []models.Product {
	key := c.categoryKey(categoryID)

	// Try to get from cache
	val, err := c.client.Get(c.ctx, key).Result()
	if err == nil {
		var products []models.Product
		if err := json.Unmarshal([]byte(val), &products); err == nil {
			return products
		}
	}

	// Fetch if not in cache
	products := fetch(categoryID)
	if len(products) > 0 {
		data, err := json.Marshal(products)
		if err == nil {
			c.client.Set(c.ctx, key, data, c.ttl)
		}
	}

	return products
}

// InvalidateProduct removes a product from cache
func (c *ProductCache) InvalidateProduct(productID string) error {
	key := c.productKey(productID)
	return c.client.Del(c.ctx, key).Err()
}

// InvalidateCategory removes a category's products from cache
func (c *ProductCache) InvalidateCategory(categoryID string) error {
	key := c.categoryKey(categoryID)
	return c.client.Del(c.ctx, key).Err()
}

// Close closes the Redis connection
func (c *ProductCache) Close() error {
	return c.client.Close()
}
