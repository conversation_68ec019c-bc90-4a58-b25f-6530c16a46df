package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/itunza/telegram-silcore/internal/models"
	"github.com/redis/go-redis/v9"
)

// ProductCache manages product caching using Redis
type ProductCache struct {
	client  *redis.Client
	ctx     context.Context
	ttl     time.Duration
	version string // Cache version to invalidate old formats
}

const currentCacheVersion = "v1"

// cachedProduct represents the structure of cached product data with versioning
type cachedProduct struct {
	Version string         `json:"version"`
	Product models.Product `json:"data"`
}

// cachedProducts represents the structure of cached product list data with versioning
type cachedProducts struct {
	Version  string           `json:"version"`
	Products []models.Product `json:"data"`
}

// cachedCategories represents the structure of cached category data with versioning
type cachedCategories struct {
	Version    string            `json:"version"`
	Categories []models.Category `json:"data"`
}

// NewProductCache creates a new product cache with Redis
func NewProductCache(redisURL string, ttl time.Duration) (*ProductCache, error) {
	opt, err := redis.ParseURL(redisURL)
	if err != nil {
		return nil, fmt.Errorf("failed to parse Redis URL: %v", err)
	}

	client := redis.NewClient(opt)
	ctx := context.Background()

	// Test connection
	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %v", err)
	}

	return &ProductCache{
		client:  client,
		ctx:     ctx,
		ttl:     ttl,
		version: currentCacheVersion,
	}, nil
}

// productKey generates a Redis key for a product
func (c *ProductCache) productKey(productID string) string {
	return fmt.Sprintf("product:%s", productID)
}

// categoryKey generates a Redis key for a category's products
func (c *ProductCache) categoryKey(categoryID string) string {
	return fmt.Sprintf("category:%s:products", categoryID)
}

// GetProduct gets a product from cache or fetches it using the provided function
func (c *ProductCache) GetProduct(productID string, fetch func(string) *models.Product) *models.Product {
	key := c.productKey(productID)

	// Try to get from cache
	val, err := c.client.Get(c.ctx, key).Result()
	if err == nil {
		var cached cachedProduct
		if err := json.Unmarshal([]byte(val), &cached); err == nil {
			if cached.Version == c.version {
				return &cached.Product
			}
			// Version mismatch - invalidate cache
			c.InvalidateProduct(productID)
		}
	}

	// Fetch if not in cache or version mismatch
	product := fetch(productID)
	if product != nil {
		data, err := json.Marshal(cachedProduct{
			Version: c.version,
			Product: *product,
		})
		if err == nil {
			c.client.Set(c.ctx, key, data, c.ttl)
		}
	}

	return product
}

// GetProducts gets products for a category from cache or fetches them
func (c *ProductCache) GetProducts(categoryID string, fetch func(string) []models.Product) []models.Product {
	key := c.categoryKey(categoryID)

	// Try to get from cache with pipeline
	pipe := c.client.Pipeline()
	existsCmd := pipe.Exists(c.ctx, key)
	getCmd := pipe.Get(c.ctx, key)
	_, err := pipe.Exec(c.ctx)

	if err == nil && existsCmd.Val() > 0 {
		val, err := getCmd.Result()
		if err == nil {
			var cached cachedProducts
			if err := json.Unmarshal([]byte(val), &cached); err == nil {
				if cached.Version == c.version {
					return cached.Products
				}
			}
		}
	}

	// Fetch if not in cache
	products := fetch(categoryID)
	if len(products) > 0 {
		data, err := json.Marshal(cachedProducts{
			Version:  c.version,
			Products: products,
		})
		if err == nil {
			pipe := c.client.Pipeline()
			pipe.Set(c.ctx, key, data, c.ttl)
			pipe.Exec(c.ctx)
		}
	}

	return products
}

// InvalidateProduct removes a product from cache
func (c *ProductCache) InvalidateProduct(productID string) error {
	key := c.productKey(productID)
	return c.client.Del(c.ctx, key).Err()
}

// InvalidateCategory removes a category's products from cache
func (c *ProductCache) InvalidateCategory(categoryID string) error {
	key := c.categoryKey(categoryID)
	return c.client.Del(c.ctx, key).Err()
}

// GetCategories retrieves categories from cache
func (c *ProductCache) GetCategories(warehouseID string) ([]models.Category, error) {
	if warehouseID == "" {
		return nil, fmt.Errorf("categories not found in cache")
	}

	key := fmt.Sprintf("categories:%s", warehouseID)
	val, err := c.client.Get(c.ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, fmt.Errorf("categories not found in cache")
		}
		return nil, err
	}

	var cached cachedCategories
	if err := json.Unmarshal([]byte(val), &cached); err != nil {
		return nil, fmt.Errorf("failed to unmarshal categories: %v", err)
	}

	if cached.Version != c.version {
		return nil, fmt.Errorf("cache version mismatch")
	}

	return cached.Categories, nil
}

// SetCategories stores categories in cache
func (c *ProductCache) SetCategories(warehouseID string, categories []models.Category) error {
	if warehouseID == "" {
		return fmt.Errorf("warehouse ID cannot be empty")
	}

	if categories == nil {
		categories = []models.Category{} // Initialize empty slice instead of nil
	}

	cached := cachedCategories{
		Version:    c.version,
		Categories: categories,
	}

	data, err := json.Marshal(cached)
	if err != nil {
		return fmt.Errorf("failed to marshal categories: %v", err)
	}

	key := fmt.Sprintf("categories:%s", warehouseID)
	return c.client.Set(c.ctx, key, data, c.ttl).Err()
}

// Close closes the Redis connection
func (c *ProductCache) Close() error {
	return c.client.Close()
}
