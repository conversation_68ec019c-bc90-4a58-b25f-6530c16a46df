package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

// SessionData represents the session data structure
type SessionData struct {
	UserID        int64                  `json:"user_id"`
	Cart          map[string]int         `json:"cart"`
	LastActive    time.Time              `json:"last_active"`
	CurrentMenu   string                 `json:"current_menu"`
	SelectedItems map[string]interface{} `json:"selected_items"`
}

// SessionService manages user sessions using Redis
type SessionService struct {
	client *redis.Client
	ctx    context.Context
}

// NewSessionService creates a new session service with Redis
func NewSessionService(redisURL string) (*SessionService, error) {
	opt, err := redis.ParseURL(redisURL)
	if err != nil {
		return nil, fmt.Errorf("failed to parse Redis URL: %v", err)
	}

	client := redis.NewClient(opt)
	ctx := context.Background()

	// Test connection
	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %v", err)
	}

	return &SessionService{
		client: client,
		ctx:    ctx,
	}, nil
}

// sessionKey generates a Redis key for a user session
func (s *SessionService) sessionKey(userID int64) string {
	return fmt.Sprintf("session:%d", userID)
}

// GetSession retrieves a user's session data
func (s *SessionService) GetSession(userID int64) *SessionData {
	key := s.sessionKey(userID)
	val, err := s.client.Get(s.ctx, key).Result()
	if err == redis.Nil {
		// Session doesn't exist, create new one
		session := &SessionData{
			UserID:        userID,
			Cart:          make(map[string]int),
			LastActive:    time.Now(),
			SelectedItems: make(map[string]interface{}),
		}
		s.SaveSession(session)
		return session
	}
	if err != nil {
		// Log error but return new session
		fmt.Printf("Error getting session: %v\n", err)
		return &SessionData{
			UserID:        userID,
			Cart:          make(map[string]int),
			LastActive:    time.Now(),
			SelectedItems: make(map[string]interface{}),
		}
	}

	var session SessionData
	if err := json.Unmarshal([]byte(val), &session); err != nil {
		fmt.Printf("Error unmarshaling session: %v\n", err)
		return &SessionData{
			UserID:        userID,
			Cart:          make(map[string]int),
			LastActive:    time.Now(),
			SelectedItems: make(map[string]interface{}),
		}
	}

	return &session
}

// SaveSession saves the session data to Redis
func (s *SessionService) SaveSession(session *SessionData) error {
	session.LastActive = time.Now()
	data, err := json.Marshal(session)
	if err != nil {
		return fmt.Errorf("failed to marshal session: %v", err)
	}

	key := s.sessionKey(session.UserID)
	// Set session with 24 hour expiry
	if err := s.client.Set(s.ctx, key, data, 24*time.Hour).Err(); err != nil {
		return fmt.Errorf("failed to save session: %v", err)
	}

	return nil
}

// ClearSession removes a user's session
func (s *SessionService) ClearSession(userID int64) error {
	key := s.sessionKey(userID)
	if err := s.client.Del(s.ctx, key).Err(); err != nil {
		return fmt.Errorf("failed to clear session: %v", err)
	}
	return nil
}

// Close closes the Redis connection
func (s *SessionService) Close() error {
	return s.client.Close()
}

// ResetSession resets all shopping-related session data
func (s *SessionData) ResetSession() {
	s.Cart = make(map[string]int)
	s.CurrentMenu = ""
	s.SelectedItems = make(map[string]interface{})
	s.LastActive = time.Now()
}
