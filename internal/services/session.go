package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

// CartItem represents an item in the cart with its warehouse
type CartItem struct {
	ProductID string  `json:"product_id"`
	Quantity  int     `json:"quantity"`
	Price     float64 `json:"price"`
	Warehouse string  `json:"warehouse"`
}

// CustomerSession represents a single customer's session data
type CustomerSession struct {
	CustomerID   string               `json:"customer_id"`
	Cart         map[string]*CartItem `json:"cart"`
	CustomerCode string               `json:"customer_code"`
	CreatedAt    time.Time            `json:"created_at"`
	LastActive   time.Time            `json:"last_active"`
}

// SessionData represents the session data structure
type SessionData struct {
	LocalUserID      int64                       `json:"local_user_id"`
	ERPNextUser      string                      `json:"erpnext_user"`
	LastActive       time.Time                   `json:"last_active"`
	IsActive         bool                        `json:"is_active"`
	ActiveCustomerID string                      `json:"active_customer_id"`
	Customers        map[string]*CustomerSession `json:"customers"`
	CurrentMenu      string                      `json:"current_menu"`
	SelectedItems    map[string]interface{}      `json:"selected_items"`
	AwaitingSearch   bool                        `json:"awaiting_search"`
	LastCategory     string                      `json:"last_category"`
	CurrentPaymentID string                      `json:"current_payment_id"`
	PaymentState     *PaymentState
}

type PaymentState struct {
	ID     string
	Method string
	Amount float64
	Status string
}

// SessionService manages user sessions using Redis
type SessionService struct {
	client *redis.Client
	ctx    context.Context
}

// NewSessionService creates a new session service with Redis
func NewSessionService(redisURL string) (*SessionService, error) {
	opt, err := redis.ParseURL(redisURL)
	if err != nil {
		return nil, fmt.Errorf("failed to parse Redis URL: %v", err)
	}

	client := redis.NewClient(opt)
	ctx := context.Background()

	// Test connection
	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %v", err)
	}

	return &SessionService{
		client: client,
		ctx:    ctx,
	}, nil
}

// sessionKey generates a Redis key for a user session
func (s *SessionService) sessionKey(userID int64) string {
	return fmt.Sprintf("session:%d", userID)
}

// GetSession retrieves a user's session data
func (s *SessionService) GetSession(userID int64) *SessionData {
	// Get session from Redis
	val, err := s.client.Get(s.ctx, s.sessionKey(userID)).Result()
	if err == redis.Nil {
		// Session doesn't exist, create new one
		return &SessionData{
			LocalUserID:   userID,
			IsActive:      false,
			LastActive:    time.Now(),
			SelectedItems: make(map[string]interface{}),
			Customers:     make(map[string]*CustomerSession),
		}
	} else if err != nil {
		// Log error but return new session
		fmt.Printf("Error retrieving session from Redis: %v\n", err)
		return &SessionData{
			LocalUserID:   userID,
			IsActive:      false,
			LastActive:    time.Now(),
			SelectedItems: make(map[string]interface{}),
			Customers:     make(map[string]*CustomerSession),
		}
	}

	// Unmarshal existing session
	var session SessionData
	if err := json.Unmarshal([]byte(val), &session); err != nil {
		fmt.Printf("Error unmarshaling session data: %v\n", err)
		return &SessionData{
			LocalUserID:   userID,
			IsActive:      false,
			LastActive:    time.Now(),
			SelectedItems: make(map[string]interface{}),
			Customers:     make(map[string]*CustomerSession),
		}
	}

	// Ensure customers map exists
	if session.Customers == nil {
		session.Customers = make(map[string]*CustomerSession)
	}

	return &session
}

// SaveSession saves the session data to Redis
func (s *SessionService) SaveSession(session *SessionData) error {
	if session.Customers == nil {
		session.Customers = make(map[string]*CustomerSession)
	}

	// Ensure default customer exists if no customers
	if len(session.Customers) == 0 {
		defaultCustomer := &CustomerSession{
			CustomerID: "default",
			Cart:       make(map[string]*CartItem),
			CreatedAt:  time.Now(),
			LastActive: time.Now(),
		}
		session.Customers["default"] = defaultCustomer
		session.ActiveCustomerID = "default"
	}

	// Marshal session data
	data, err := json.Marshal(session)
	if err != nil {
		return fmt.Errorf("failed to marshal session data: %v", err)
	}

	// Save to Redis with 24-hour expiry
	err = s.client.Set(s.ctx, s.sessionKey(session.LocalUserID), data, 24*time.Hour).Err()
	if err != nil {
		return fmt.Errorf("failed to save session to Redis: %v", err)
	}

	return nil
}

// ClearSession removes a user's session
func (s *SessionService) ClearSession(userID int64) error {
	// Delete session from Redis
	err := s.client.Del(s.ctx, s.sessionKey(userID)).Err()
	if err != nil {
		return fmt.Errorf("failed to clear session: %v", err)
	}
	return nil
}

// Close closes the Redis connection
func (s *SessionService) Close() error {
	return s.client.Close()
}

// NewCustomerSession creates a new customer session
func (s *SessionData) NewCustomerSession(customerID string) *CustomerSession {
	if s.Customers == nil {
		s.Customers = make(map[string]*CustomerSession)
	}

	session := &CustomerSession{
		CustomerID: customerID,
		Cart:       make(map[string]*CartItem),
		CreatedAt:  time.Now(),
		LastActive: time.Now(),
	}
	s.Customers[customerID] = session
	s.ActiveCustomerID = customerID
	return session
}

// GetActiveCustomer returns the active customer's session
func (s *SessionData) GetActiveCustomer() *CustomerSession {
	if s.ActiveCustomerID == "" || s.Customers == nil {
		return nil
	}
	return s.Customers[s.ActiveCustomerID]
}

// GetCart returns the active customer's cart
func (s *SessionData) GetCart() map[string]*CartItem {
	customer := s.GetActiveCustomer()
	if customer == nil {
		return make(map[string]*CartItem)
	}
	return customer.Cart
}

// ResetSession resets all shopping-related session data
func (s *SessionData) ResetSession() {
	s.Customers = make(map[string]*CustomerSession)
	s.CurrentMenu = ""
	s.SelectedItems = make(map[string]interface{})
	s.LastActive = time.Now()
}

// IsAuthenticated checks if the session is authenticated
func (s *SessionData) IsAuthenticated() bool {
	return s.IsActive && s.ERPNextUser != ""
}
