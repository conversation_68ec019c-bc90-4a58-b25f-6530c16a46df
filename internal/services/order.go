package services

import (
	"fmt"
	"log"
	"strings"
	"time"
)

type OrderService struct {
	erpnext *ERPNextClient
	shop    *ShopService
}

func NewOrderService(erpnext *ERPNextClient, shop *ShopService) *OrderService {
	return &OrderService{
		erpnext: erpnext,
		shop:    shop,
	}
}

func (s *OrderService) GenerateOrderSummary(cart map[string]*CartItem) (string, float64, error) {
	var total float64
	summary := &strings.Builder{}

	// Group items by warehouse
	warehouseItems := make(map[string][]*CartItem)
	for productID, item := range cart {
		item.ProductID = productID // Ensure ProductID is set
		warehouseItems[item.Warehouse] = append(warehouseItems[item.Warehouse], item)
	}

	// Generate summary by warehouse
	for warehouse, items := range warehouseItems {
		summary.WriteString(fmt.Sprintf("\n*Warehouse: %s*\n", warehouse))
		summary.WriteString("```\n") // Start monospace block

		// Find longest product name for alignment
		maxNameLen := 0
		for _, item := range items {
			product := s.shop.GetProduct(item.ProductID, warehouse)
			if product != nil && len(product.Name) > maxNameLen {
				maxNameLen = len(product.Name)
			}
		}

		// Add items
		format := fmt.Sprintf("%%-%ds  %%8s  %%8s  %%10s\n", maxNameLen)
		summary.WriteString(fmt.Sprintf(format, "Product", "Price", "Qty", "Subtotal"))
		summary.WriteString(strings.Repeat("-", maxNameLen+30) + "\n")

		warehouseTotal := 0.0
		for _, item := range items {
			product := s.shop.GetProduct(item.ProductID, warehouse)
			if product == nil {
				continue
			}
			subtotal := float64(item.Quantity) * product.Price
			warehouseTotal += subtotal
			summary.WriteString(fmt.Sprintf(fmt.Sprintf("%%-%ds  %%8.2f  %%8d  %%10.2f\n",
				maxNameLen),
				product.Name,
				product.Price,
				item.Quantity,
				subtotal,
			))
		}

		summary.WriteString(strings.Repeat("-", maxNameLen+30) + "\n")
		summary.WriteString(fmt.Sprintf(fmt.Sprintf("%%%ds  %%10.2f\n",
			maxNameLen+19),
			"Warehouse Total:",
			warehouseTotal,
		))
		summary.WriteString("```\n") // End monospace block
		total += warehouseTotal
	}

	return summary.String(), total, nil
}

func (s *OrderService) CreateOrders(userID int64, cart map[string]*CartItem) ([]string, error) {
	// Group items by warehouse
	warehouseOrders := make(map[string][]map[string]interface{})

	// Validate all items first
	for productID, cartItem := range cart {
		product := s.shop.GetProduct(productID, cartItem.Warehouse)
		if product == nil {
			return nil, fmt.Errorf("product %s not found", productID)
		}
		if cartItem.Quantity > product.StockQty {
			return nil, fmt.Errorf("insufficient stock for product %s: requested %d, available %d",
				product.Name, cartItem.Quantity, product.StockQty)
		}
	}

	// Group items by warehouse
	for productID, cartItem := range cart {
		product := s.shop.GetProduct(productID, cartItem.Warehouse)
		orderItem := map[string]interface{}{
			"item_code": product.ID,
			"qty":       cartItem.Quantity,
			"rate":      product.Price,
			"warehouse": cartItem.Warehouse,
		}
		warehouseOrders[cartItem.Warehouse] = append(
			warehouseOrders[cartItem.Warehouse],
			orderItem,
		)
	}

	var orderRefs []string
	for warehouse, items := range warehouseOrders {
		orderData := map[string]interface{}{
			"doctype":       "Sales Order",
			"customer":      fmt.Sprintf("TG_%d", userID),
			"order_type":    "Shopping Cart",
			"items":         items,
			"set_warehouse": warehouse,
			"status":        "Draft",
			"platform":      "Telegram Bot",
			"order_source":  "Telegram",
		}

		orderRef, err := s.erpnext.CreateDocument(orderData)
		if err != nil {
			// Rollback previous orders
			for _, ref := range orderRefs {
				if err := s.erpnext.DeleteDocument("Sales Order", ref); err != nil {
					log.Printf("Failed to rollback order %s: %v", ref, err)
				}
			}
			return nil, fmt.Errorf("failed to create order for warehouse %s: %v", warehouse, err)
		}
		orderRefs = append(orderRefs, orderRef.Get("name").(string))
	}

	return orderRefs, nil
}

func (s *OrderService) GetOrderStatus(orderRef string) (string, error) {
	order, err := s.erpnext.GetDocument("Sales Order", orderRef)
	if err != nil {
		return "", fmt.Errorf("failed to get order status: %w", err)
	}
	return order.Get("status").(string), nil
}

// CreateDraftOrder creates a draft sales order in ERPNext
func (s *OrderService) CreateDraftOrder(userID int64, cart map[string]*CartItem) ([]string, error) {
	// Group items by warehouse
	warehouseOrders := make(map[string][]map[string]interface{})

	// First validate all products
	for productID, cartItem := range cart {
		product := s.shop.GetProduct(productID, cartItem.Warehouse)
		if product == nil {
			return nil, fmt.Errorf("product %s not found", productID)
		}

		// Create order item and group by warehouse
		orderItem := map[string]interface{}{
			"item_code": product.ID,
			"qty":       cartItem.Quantity,
			"rate":      product.Price,
			"warehouse": cartItem.Warehouse,
		}
		warehouseOrders[cartItem.Warehouse] = append(
			warehouseOrders[cartItem.Warehouse],
			orderItem,
		)
	}

	// Set delivery date to tomorrow
	tomorrow := time.Now().AddDate(0, 0, 1).Format("2006-01-02")

	var orderRefs []string
	// Create draft orders for each warehouse
	for warehouse, items := range warehouseOrders {
		orderData := map[string]interface{}{
			"doctype":       "Sales Order",
			"customer":      "WALK-IN",
			"naming_series": "SO-",
			"status":        "Draft",
			"set_warehouse": warehouse,
			"delivery_date": tomorrow, // Add delivery date
			"items":         items,
			"company":       "Tuiyo Farmers Cooperative Society",
		}

		// Create the draft order
		resp, err := s.erpnext.CreateDocument(orderData)
		if err != nil {
			log.Printf("Error creating draft order: %v", err)
			// Rollback previous orders on failure
			for _, ref := range orderRefs {
				if err := s.erpnext.DeleteDocument("Sales Order", ref); err != nil {
					log.Printf("Failed to rollback order %s: %v", ref, err)
				}
			}
			return nil, fmt.Errorf("failed to create draft order for warehouse %s: %w", warehouse, err)
		}

		// Log the full response
		// log.Printf("ERPNext response for draft order: %+v", resp)

		// Extract the order reference from the response
		// Based on the response structure, we need to access the name field in the data map
		var orderRef string
		if data, ok := resp.Message["data"].(map[string]interface{}); ok {
			if name, ok := data["name"].(string); ok {
				orderRef = name
			}
		}

		if orderRef == "" {
			log.Printf("Could not extract order reference from response: %+v", resp)
			// Try alternative extraction method
			if name, ok := resp.Get("name").(string); ok {
				orderRef = name
			} else {
				log.Printf("Failed to extract order reference using alternative method")
				// Rollback previous orders
				for _, ref := range orderRefs {
					if err := s.erpnext.DeleteDocument("Sales Order", ref); err != nil {
						log.Printf("Failed to rollback order %s: %v", ref, err)
					}
				}
				return nil, fmt.Errorf("invalid order reference format")
			}
		}

		log.Printf("Successfully created draft order: %s", orderRef)
		orderRefs = append(orderRefs, orderRef)
	}

	return orderRefs, nil
}
