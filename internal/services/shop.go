package services

import (
	"fmt"
	"log"
	"strings"
	"sync"
	"time"

	"github.com/itunza/telegram-silcore/internal/models"
)

// ShopService handles shop-related operations
type ShopService struct {
	erpnext *ERPNextClient
	cache   *ProductCache
	sync.RWMutex
}

// NewShopService creates a new ShopService instance
func NewShopService(erpnext *ERPNextClient, redisURL string) (*ShopService, error) {
	cache, err := NewProductCache(redisURL, 5*time.Minute)
	if err != nil {
		return nil, fmt.Errorf("failed to create product cache: %w", err)
	}
	return &ShopService{
		erpnext: erpnext,
		cache:   cache,
	}, nil
}

// GetCategories returns all available categories from ERPNext
func (s *ShopService) GetCategories() []models.Category {
	categories, err := s.erpnext.GetCategories()
	if err != nil {
		// Log error but return empty slice instead of failing
		fmt.Printf("Error fetching categories: %v\n", err)
		return []models.Category{}
	}

	fmt.Printf("Raw categories from ERPNext: %+v\n", categories)

	// First pass: Build a map of parent categories and identify system categories
	systemCategories := map[string]bool{
		"All Item Groups": true, // Root category
	}
	parentCategories := make(map[string]bool)
	for _, cat := range categories {
		name, _ := cat["name"].(string)
		parentGroup, _ := cat["parent_item_group"].(string)

		// Skip empty names
		if name == "" {
			continue
		}

		// Mark system categories
		if strings.HasPrefix(strings.ToLower(name), "raw ") ||
			strings.HasSuffix(strings.ToLower(name), " fees") ||
			strings.HasSuffix(strings.ToLower(name), "material") ||
			strings.HasSuffix(strings.ToLower(name), "services") ||
			strings.HasSuffix(strings.ToLower(name), "assemblies") ||
			strings.HasSuffix(strings.ToLower(name), "deductions") ||
			strings.HasSuffix(strings.ToLower(name), "consumable") {
			systemCategories[name] = true
			continue
		}

		// Track parent categories
		if parentGroup != "" && parentGroup != "All Item Groups" {
			parentCategories[parentGroup] = true
		}
	}

	// Second pass: Build the category list
	var result []models.Category
	for _, cat := range categories {
		name, _ := cat["name"].(string)
		parentGroup, _ := cat["parent_item_group"].(string)
		isGroupNum, _ := cat["is_group"].(float64)
		isGroup := isGroupNum == 1

		fmt.Printf("Processing category: name=%s, parent=%s, isGroup=%v\n", name, parentGroup, isGroup)

		// Skip empty or system categories
		if name == "" || systemCategories[name] {
			fmt.Printf("Skipping category %s (empty/system)\n", name)
			continue
		}

		// Include if:
		// 1. It's a parent category that has children (isGroup=1 and has children)
		// 2. It's a leaf category (isGroup=0) with a valid parent
		if (isGroup && parentCategories[name]) || (!isGroup && parentGroup != "All Item Groups") {
			result = append(result, models.Category{
				ID:       name,
				Name:     name,
				ParentID: parentGroup,
				IsGroup:  isGroup,
			})
			fmt.Printf("Added category to result: %s\n", name)
		} else {
			fmt.Printf("Skipping category %s (not a valid shopping category)\n", name)
		}
	}

	fmt.Printf("Final categories: %+v\n", result)
	return result
}

// GetProduct gets a single product by ID
func (s *ShopService) GetProduct(productID string) *models.Product {
	return s.cache.GetProduct(productID, func(id string) *models.Product {
		product, err := s.erpnext.GetProduct(id)
		if err != nil {
			fmt.Printf("Error fetching product: %v\n", err)
			return nil
		}

		if product == nil {
			return nil
		}

		// Get the price from price_list_rate
		var price float64
		if p, ok := product["price_list_rate"].(float64); ok {
			price = p
		}

		// Get the stock quantity from actual_qty
		var stockQty int
		if qty, ok := product["actual_qty"].(float64); ok {
			stockQty = int(qty)
		}

		// Get the product name
		name, _ := product["item_name"].(string)
		if name == "" {
			name, _ = product["name"].(string)
		}

		return &models.Product{
			ID:         product["name"].(string),
			CategoryID: product["item_group"].(string),
			Name:       name,
			Price:      price,
			StockQty:   stockQty,
		}
	})
}

// GetProductsByCategory returns all products in a category from ERPNext
func (s *ShopService) GetProductsByCategory(categoryID string) []models.Product {
	return s.cache.GetProducts(categoryID, func(catID string) []models.Product {
		products, err := s.erpnext.GetProductsByCategory(catID)
		if err != nil {
			fmt.Printf("Error fetching products: %v\n", err)
			return []models.Product{}
		}

		var result []models.Product
		for _, prod := range products {
			// Get the price from price_list_rate
			var price float64
			if p, ok := prod["price_list_rate"].(float64); ok {
				price = p
			}

			// Get the stock quantity from actual_qty
			var stockQty int
			if qty, ok := prod["actual_qty"].(float64); ok {
				stockQty = int(qty)
			}

			// Get the product name
			name, _ := prod["item_name"].(string)
			if name == "" {
				name, _ = prod["name"].(string)
			}

			result = append(result, models.Product{
				ID:         prod["name"].(string),
				CategoryID: categoryID,
				Name:       name,
				Price:      price,
				StockQty:   stockQty,
			})
		}

		return result
	})
}

// GetProducts gets all products in a category
func (s *ShopService) GetProducts(categoryID string) []models.Product {
	items, err := s.erpnext.GetProductsByCategory(categoryID)
	if err != nil {
		fmt.Printf("Error fetching products for category %s: %v\n", categoryID, err)
		return []models.Product{}
	}

	var products []models.Product
	for _, item := range items {
		name, _ := item["name"].(string)
		itemName, _ := item["item_name"].(string)
		itemGroup, _ := item["item_group"].(string)
		rate, _ := item["price_list_rate"].(float64)
		qty, _ := item["actual_qty"].(float64)

		products = append(products, models.Product{
			ID:         name,
			Name:       itemName,
			CategoryID: itemGroup,
			Price:      rate,
			StockQty:   int(qty),
		})
	}

	return products
}

// CreateOrder creates a new sales order in ERPNext
func (s *ShopService) CreateOrder(userID int64, cart map[string]int) error {
	if len(cart) == 0 {
		return fmt.Errorf("cart is empty")
	}

	// Convert cart items to ERPNext order items
	var items []map[string]interface{}
	for productID, qty := range cart {
		product := s.GetProduct(productID)
		if product == nil {
			continue
		}

		items = append(items, map[string]interface{}{
			"item_code": product.ID,
			"qty":       qty,
			"rate":      product.Price,
		})
	}

	// Create the order
	orderData := map[string]interface{}{
		"doctype":       "Sales Order",
		"customer":      fmt.Sprintf("TG_%d", userID), // Prefix with TG_ to identify Telegram users
		"order_type":    "Shopping Cart",
		"items":         items,
		"set_warehouse": "Stores - M", // Update this to match your ERPNext warehouse
	}

	// _, err := s.erpnext.CreateDocument(orderData)
	// if err != nil {
	// 	return fmt.Errorf("failed to create order: %v", err)
	// }

	log.Printf("Order created: %v", orderData)

	return nil
}
