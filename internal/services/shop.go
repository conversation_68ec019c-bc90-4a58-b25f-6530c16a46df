package services

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"time"

	"github.com/itunza/telegram-silcore/internal/config"
	"github.com/itunza/telegram-silcore/internal/models"
	"github.com/itunza/telegram-silcore/internal/types"
	_ "github.com/lib/pq"
)

// Add constant for default warehouse
const (
	DefaultWarehouse = "Stores - M" // Or your preferred default
)

// ShopService handles shop-related operations
type ShopService struct {
	erpnext *ERPNextClient
	cache   *ProductCache
	db      *sql.DB
}

// NewShopService creates a new ShopService instance with PostgreSQL integration
func NewShopService(erpnext *ERPNextClient, redisURL string, config *config.Config) (*ShopService, error) {
	cache, err := NewProductCache(redisURL, 5*time.Minute)
	if err != nil {
		return nil, fmt.Errorf("failed to create product cache: %w", err)
	}

	// Build PostgreSQL connection string from environment variables
	connStr := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=disable",
		config.DBHost, config.DBPort, config.DBUser, config.DBPassword, config.DBName)
	db, err := sql.Open("postgres", connStr)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Verify database connection
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	return &ShopService{
		erpnext: erpnext,
		cache:   cache,
		db:      db,
	}, nil
}

// Close closes all connections
func (s *ShopService) Close() error {
	if s.db != nil {
		if err := s.db.Close(); err != nil {
			return fmt.Errorf("failed to close database connection: %w", err)
		}
	}
	if s.cache != nil {
		if err := s.cache.Close(); err != nil {
			return fmt.Errorf("failed to close cache: %w", err)
		}
	}
	return nil
}

// GetCategories returns all product categories for a given warehouse
func (s *ShopService) GetCategories(warehouseID string) ([]models.Category, error) {
	// Use default warehouse if none specified
	if warehouseID == "" {
		warehouseID = DefaultWarehouse
	}

	// Try to get from cache first
	categories, err := s.cache.GetCategories(warehouseID)
	if err == nil && len(categories) > 0 {
		return categories, nil
	}

	// If not in cache or error, fetch from ERPNext
	rawCategories, err := s.erpnext.GetCategoriesForWarehouse(warehouseID)
	if err != nil {
		log.Printf("Error fetching categories from ERPNext: %v", err)
		return nil, fmt.Errorf("failed to fetch categories: %v", err)
	}

	// Map the raw categories to our model
	categories = mapERPNextToCategories(rawCategories)

	// Cache the results
	if err := s.cache.SetCategories(warehouseID, categories); err != nil {
		log.Printf("Warning: failed to cache categories: %v", err)
		// Don't return error here, just log it
	}

	return categories, nil
}

// GetCategoriesForWarehouse returns categories filtered by warehouse
func (s *ShopService) GetCategoriesForWarehouse(warehouse string) []models.Category {
	// Use default warehouse if none specified
	if warehouse == "" {
		warehouse = DefaultWarehouse
	}

	categories, err := s.GetCategories(warehouse)
	if err != nil {
		log.Printf("Error fetching categories for warehouse %s: %v", warehouse, err)
		return []models.Category{}
	}
	return categories
}

// GetProduct gets a single product by ID
func (s *ShopService) GetProduct(productID string, warehouse string) *models.Product {
	return s.cache.GetProduct(productID, func(id string) *models.Product {
		product, err := s.erpnext.GetProduct(id, warehouse)
		if err != nil {
			log.Printf("Error fetching product %s: %v", id, err)
			return nil
		}

		if product == nil {
			return nil
		}

		return mapERPNextToProduct(product)
	})
}

// GetProductsByCategory returns all products in a category for a specific warehouse
func (s *ShopService) GetProductsByCategory(categoryID string, warehouse string) []models.Product {
	// Validate inputs
	if categoryID == "" {
		log.Printf("Warning: GetProductsByCategory called with empty categoryID")
		return []models.Product{}
	}

	// Use default warehouse if none specified
	if warehouse == "" {
		warehouse = DefaultWarehouse
	}

	// Create cache key that includes both category and warehouse
	cacheKey := fmt.Sprintf("category:%s:warehouse:%s:products", categoryID, warehouse)

	// Try to get from cache first
	return s.cache.GetProducts(cacheKey, func(key string) []models.Product {
		// Fetch from ERPNext if not in cache
		products, err := s.erpnext.GetProductsByCategoryAndWarehouse(categoryID, warehouse)
		if err != nil {
			log.Printf("Error fetching products for category %s and warehouse %s: %v",
				categoryID, warehouse, err)
			return []models.Product{}
		}

		// Map ERPNext products to our model
		return mapERPNextToProducts(products)
	})
}

// GetProductsByCategoryAndWarehouse returns products filtered by category and warehouse
func (s *ShopService) GetProductsByCategoryAndWarehouse(categoryID, warehouse string) []models.Product {
	cacheKey := fmt.Sprintf("category:%s:warehouse:%s:products", categoryID, warehouse)
	return s.cache.GetProducts(cacheKey, func(key string) []models.Product {
		products, err := s.erpnext.GetProductsByCategoryAndWarehouse(categoryID, warehouse)
		if err != nil {
			log.Printf("Error fetching products for category %s and warehouse %s: %v",
				categoryID, warehouse, err)
			return []models.Product{}
		}
		return mapERPNextToProducts(products)
	})
}

// Helper function to map ERPNext product data to our Product model
func mapERPNextToProduct(p map[string]interface{}) *models.Product {
	var price float64
	if p, ok := p["price_list_rate"].(float64); ok {
		price = p
	}

	var stockQty int
	if qty, ok := p["actual_qty"].(float64); ok {
		stockQty = int(qty)
	}

	name, _ := p["item_name"].(string)
	if name == "" {
		name, _ = p["name"].(string)
	}

	return &models.Product{
		ID:         p["name"].(string),
		CategoryID: p["item_group"].(string),
		Name:       name,
		Price:      price,
		StockQty:   stockQty,
	}
}

// Helper function to map ERPNext products to our Product models
func mapERPNextToProducts(products []map[string]interface{}) []models.Product {
	var result []models.Product
	for _, p := range products {
		if product := mapERPNextToProduct(p); product != nil {
			result = append(result, *product)
		}
	}
	return result
}

// SearchProductsByCategory searches for products in a category by a search term
func (s *ShopService) SearchProductsByCategory(categoryID string, searchTerm string, userID int64) []models.Product {
	return s.cache.GetProducts(categoryID+":search:"+searchTerm, func(key string) []models.Product {
		// Get user's warehouse first
		warehouse, err := s.GetUserWarehouse(userID)
		if err != nil {
			fmt.Printf("Error getting user warehouse: %v\n", err)
			return []models.Product{}
		}

		products, err := s.erpnext.SearchProductsByCategory(categoryID, searchTerm, warehouse)
		if err != nil {
			fmt.Printf("Error searching products: %v\n", err)
			return []models.Product{}
		}

		var result []models.Product
		for _, p := range products {
			// Get the price from price_list_rate
			var price float64
			if p, ok := p["price_list_rate"].(float64); ok {
				price = p
			}

			// Get the stock quantity from actual_qty
			var stockQty int
			if qty, ok := p["actual_qty"].(float64); ok {
				stockQty = int(qty)
			}

			// Get the product name
			name, _ := p["item_name"].(string)
			if name == "" {
				name, _ = p["name"].(string)
			}

			result = append(result, models.Product{
				ID:         p["name"].(string),
				CategoryID: p["item_group"].(string),
				Name:       name,
				Price:      price,
				StockQty:   stockQty,
			})
		}

		return result
	})
}

// SearchProducts searches for products across all categories
func (s *ShopService) SearchProducts(searchTerm string) []models.Product {
	return s.cache.GetProducts("search:"+searchTerm, func(key string) []models.Product {
		products, err := s.erpnext.SearchProducts(searchTerm)
		if err != nil {
			fmt.Printf("Error searching products: %v\n", err)
			return []models.Product{}
		}

		var result []models.Product
		for _, p := range products {
			// Get the price from price_list_rate
			var price float64
			if p, ok := p["price_list_rate"].(float64); ok {
				price = p
			}

			// Get the stock quantity from actual_qty
			var stockQty int
			if qty, ok := p["actual_qty"].(float64); ok {
				stockQty = int(qty)
			}

			// Get the product name
			name, _ := p["item_name"].(string)
			if name == "" {
				name, _ = p["name"].(string)
			}

			result = append(result, models.Product{
				ID:         p["name"].(string),
				CategoryID: p["item_group"].(string),
				Name:       name,
				Price:      price,
				StockQty:   stockQty,
			})
		}

		return result
	})
}

// GetUserWarehouse returns the currently selected (default) warehouse for a user
func (s *ShopService) GetUserWarehouse(userID int64) (string, error) {
	var warehouse string
	err := s.db.QueryRow(`
		SELECT warehouse_id 
		FROM user_warehouses 
		WHERE user_id = $1 AND is_default = true
		LIMIT 1`,
		userID).Scan(&warehouse)

	if err == sql.ErrNoRows {
		return "", nil // No default warehouse selected
	}
	if err != nil {
		return "", fmt.Errorf("failed to get warehouse: %w", err)
	}

	return warehouse, nil
}

// SetUserWarehouse sets the warehouse for a user
func (s *ShopService) SetUserWarehouse(userID int64, warehouse string) error {
	// Validate warehouse exists in ERPNext
	warehouses, err := s.erpnext.GetWarehouses()
	if err != nil {
		return fmt.Errorf("failed to validate warehouse: %w", err)
	}

	warehouseExists := false
	for _, w := range warehouses {
		if name, ok := w["name"].(string); ok && name == warehouse {
			warehouseExists = true
			break
		}
	}

	if !warehouseExists {
		return fmt.Errorf("warehouse %s does not exist", warehouse)
	}

	// Begin transaction
	tx, err := s.db.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// First, unset any existing default warehouse for this user
	_, err = tx.Exec(`
		UPDATE user_warehouses 
		SET is_default = false 
		WHERE user_id = $1 AND is_default = true`,
		userID)
	if err != nil {
		return fmt.Errorf("failed to unset default warehouse: %w", err)
	}

	// Then insert or update the new warehouse selection
	_, err = tx.Exec(`
		INSERT INTO user_warehouses (
			user_id, 
			warehouse_id, 
			is_default, 
			assigned_at
		) VALUES ($1, $2, true, CURRENT_TIMESTAMP)
		ON CONFLICT (user_id, warehouse_id) 
		DO UPDATE SET 
			is_default = true,
			assigned_at = CURRENT_TIMESTAMP`,
		userID, warehouse)
	if err != nil {
		return fmt.Errorf("failed to set warehouse: %w", err)
	}

	if err = tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// Customer represents a customer with their cart

// CartItem represents an item in the customer's cart

func (s *ShopService) CreateOrder(ctx context.Context, customer *types.Customer) (string, error) {
	if len(customer.Cart) == 0 {
		return "", fmt.Errorf("cart is empty")
	}

	orderItems := make([]map[string]interface{}, 0)
	var warehouse string

	// Convert cart items to order items
	for _, item := range customer.Cart {
		orderItems = append(orderItems, map[string]interface{}{
			"item_code": item.ProductID,
			"qty":       item.Quantity,
			"rate":      item.Price,
			"warehouse": item.Warehouse, // Use warehouse from cart item
		})

		// Set the order-level warehouse from the first item
		if warehouse == "" {
			warehouse = item.Warehouse
		}
	}

	orderData := map[string]interface{}{
		"doctype":       "Sales Order",
		"customer":      "WALK-IN",
		"delivery_date": time.Now().Format("2006-01-02"),
		"order_type":    "Sales",
		"items":         orderItems,
		"set_warehouse": warehouse, // Set warehouse at order level
	}

	// Create the sales order in ERPNext
	doc, err := s.erpnext.CreateDocument(orderData)
	if err != nil {
		return "", fmt.Errorf("failed to create order: %w", err)
	}

	return doc.Get("name").(string), nil
}

// WarehouseAssignment represents a user's warehouse assignment
type WarehouseAssignment struct {
	WarehouseID string    `json:"warehouse_id"`
	IsDefault   bool      `json:"is_default"`
	AssignedAt  time.Time `json:"assigned_at"`
}

// AssignWarehouseToUser assigns a warehouse to a user
func (s *ShopService) AssignWarehouseToUser(userID int64, warehouse string) error {
	// Validate warehouse exists in ERPNext
	exists, err := s.erpnext.CheckWarehouseExists(warehouse)
	if err != nil {
		return fmt.Errorf("failed to validate warehouse: %w", err)
	}
	if !exists {
		return fmt.Errorf("warehouse %s does not exist", warehouse)
	}

	// Store warehouse preference in Redis
	key := fmt.Sprintf("user:%d:warehouse", userID)
	err = s.cache.client.Set(context.Background(), key, warehouse, 0).Err()
	if err != nil {
		return fmt.Errorf("failed to set warehouse: %w", err)
	}

	return nil
}

// GetUserWarehouses returns all warehouses available from ERPNext
func (s *ShopService) GetUserWarehouses(userID int64) ([]WarehouseAssignment, error) {
	// Get all warehouses from ERPNext
	warehouses, err := s.erpnext.GetWarehouses()
	if err != nil {
		return nil, fmt.Errorf("failed to fetch warehouses: %w", err)
	}

	// Get user's current warehouse
	currentWarehouse, err := s.GetUserWarehouse(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get current warehouse: %w", err)
	}

	var result []WarehouseAssignment
	for _, w := range warehouses {
		warehouseName, ok := w["name"].(string)
		if !ok {
			continue
		}

		result = append(result, WarehouseAssignment{
			WarehouseID: warehouseName,
			IsDefault:   warehouseName == currentWarehouse,
			AssignedAt:  time.Now(), // Since we're not storing this, use current time
		})
	}

	return result, nil
}

// RemoveWarehouse removes a warehouse assignment from a user
func (s *ShopService) RemoveWarehouse(userID int64) error {
	key := fmt.Sprintf("user:%d:warehouse", userID)
	err := s.cache.client.Del(context.Background(), key).Err()
	if err != nil {
		return fmt.Errorf("failed to remove warehouse: %w", err)
	}
	return nil
}

// Helper function to map ERPNext categories to our Category models
func mapERPNextToCategories(rawCategories []map[string]interface{}) []models.Category {
	var categories []models.Category
	for _, c := range rawCategories {
		name, _ := c["name"].(string)
		parentGroup, _ := c["parent_item_group"].(string)
		isGroup, _ := c["is_group"].(bool)

		category := models.Category{
			ID:       name,
			Name:     name,
			ParentID: parentGroup,
			IsGroup:  isGroup,
		}
		categories = append(categories, category)
	}
	return categories
}
