package services

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"strings"
)

// ERPNextClient handles communication with ERPNext
type ERPNextClient struct {
	BaseURL    string
	APIKey     string
	APISecret  string
	httpClient *http.Client
}

// ERPNextResponse represents the standard ERPNext API response
type ERPNextResponse struct {
	Message interface{} `json:"message"`
}

// NewERPNextClient creates a new ERPNext client
func NewERPNextClient(baseURL, apiKey, apiSecret string) (*ERPNextClient, error) {
	if baseURL == "" || apiKey == "" || apiSecret == "" {
		return nil, fmt.Errorf("ERPNext configuration missing. Please provide baseURL, apiKey, and apiSecret")
	}

	// Ensure base URL ends with /api
	if !strings.HasSuffix(baseURL, "/api") {
		baseURL = strings.TrimSuffix(baseURL, "/") + "/api"
	}

	return &ERPNextClient{
		BaseURL:    baseURL,
		APIKey:     apiKey,
		APISecret:  apiSecret,
		httpClient: &http.Client{},
	}, nil
}

// GetCategories fetches item groups from ERPNext
func (c *ERPNextClient) GetCategories() ([]map[string]interface{}, error) {
	endpoint := "/resource/Item Group"
	params := url.Values{}
	params.Add("fields", `["name", "parent_item_group", "is_group"]`)

	resp, err := c.makeRequest("GET", endpoint, params)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch categories: %v", err)
	}

	// Debug the response
	fmt.Printf("Response Message type: %T\n", resp.Message)
	fmt.Printf("Response Message: %+v\n", resp.Message)

	// Parse the response
	if data, ok := resp.Message.(map[string]interface{}); ok {
		fmt.Printf("Got data map: %+v\n", data)
		if items, ok := data["data"].([]interface{}); ok {
			fmt.Printf("Got items slice: %+v\n", items)
			categories := make([]map[string]interface{}, 0)
			for _, item := range items {
				fmt.Printf("Processing item: %+v\n", item)
				if cat, ok := item.(map[string]interface{}); ok {
					categories = append(categories, cat)
				}
			}
			return categories, nil
		} else {
			fmt.Printf("Failed to cast data to []interface{}: %T\n", data["data"])
		}
	} else {
		fmt.Printf("Failed to cast Message to map: %T\n", resp.Message)
	}

	return []map[string]interface{}{}, nil
}

// GetProductsByCategory fetches items from ERPNext for a specific category
func (c *ERPNextClient) GetProductsByCategory(categoryID string) ([]map[string]interface{}, error) {
	// First get the items
	endpoint := "/resource/Item"
	params := url.Values{}
	params.Add("fields", `[
		"name",
		"item_name",
		"item_code",
		"item_group",
		"stock_uom",
		"weight_per_unit",
		"brand",
		"image",
		"disabled"
	]`)
	params.Add("filters", fmt.Sprintf(`[["item_group", "=", "%s"], ["disabled", "=", 0]]`, categoryID))

	resp, err := c.makeRequest("GET", endpoint, params)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch products: %v", err)
	}

	var items []map[string]interface{}
	if msg, ok := resp.Message.(map[string]interface{}); ok {
		if data, ok := msg["data"].([]interface{}); ok {
			for _, item := range data {
				if prod, ok := item.(map[string]interface{}); ok {
					items = append(items, prod)
				}
			}
		}
	}

	// Then get the prices for these items
	if len(items) > 0 {
		var itemCodes []string
		for _, item := range items {
			if code, ok := item["item_code"].(string); ok {
				itemCodes = append(itemCodes, code)
			}
		}

		// Get prices
		priceEndpoint := "/resource/Item Price"
		priceParams := url.Values{}
		priceParams.Add("fields", `["item_code", "price_list_rate"]`)
		priceParams.Add("filters", fmt.Sprintf(`[["item_code", "in", "%s"], ["selling", "=", 1]]`, strings.Join(itemCodes, ", ")))

		priceResp, err := c.makeRequest("GET", priceEndpoint, priceParams)
		if err != nil {
			log.Printf("Warning: failed to fetch prices: %v", err)
		} else {
			// Create a map of item code to price
			prices := make(map[string]float64)
			if msg, ok := priceResp.Message.(map[string]interface{}); ok {
				if data, ok := msg["data"].([]interface{}); ok {
					for _, price := range data {
						if p, ok := price.(map[string]interface{}); ok {
							if code, ok := p["item_code"].(string); ok {
								if rate, ok := p["price_list_rate"].(float64); ok {
									prices[code] = rate
								}
							}
						}
					}
				}
			}

			// Add prices to items
			for i, item := range items {
				if code, ok := item["item_code"].(string); ok {
					if price, exists := prices[code]; exists {
						items[i]["price_list_rate"] = price
					}
				}
			}
		}

		// Get stock quantities
		binEndpoint := "/resource/Bin"
		binParams := url.Values{}
		binParams.Add("fields", `["item_code", "actual_qty"]`)
		binParams.Add("filters", fmt.Sprintf(`[["item_code", "in", "%s"]]`, strings.Join(itemCodes, ", ")))

		binResp, err := c.makeRequest("GET", binEndpoint, binParams)
		if err != nil {
			log.Printf("Warning: failed to fetch stock quantities: %v", err)
		} else {
			// Create a map of item code to quantity
			quantities := make(map[string]float64)
			if msg, ok := binResp.Message.(map[string]interface{}); ok {
				if data, ok := msg["data"].([]interface{}); ok {
					for _, bin := range data {
						if b, ok := bin.(map[string]interface{}); ok {
							if code, ok := b["item_code"].(string); ok {
								if qty, ok := b["actual_qty"].(float64); ok {
									quantities[code] += qty // Sum up quantities across all bins
								}
							}
						}
					}
				}
			}

			// Add quantities to items
			for i, item := range items {
				if code, ok := item["item_code"].(string); ok {
					items[i]["actual_qty"] = quantities[code]
				}
			}
		}
	}

	return items, nil
}

// GetProduct fetches a single item from ERPNext
func (c *ERPNextClient) GetProduct(productID string) (map[string]interface{}, error) {
	// First get the item details
	endpoint := "/resource/Item"
	params := url.Values{}
	params.Add("filters", fmt.Sprintf(`[["name", "=", "%s"]]`, productID))
	params.Add("fields", `["name", "item_name", "item_code", "item_group", "stock_uom", "weight_per_unit", "brand", "image", "disabled"]`)

	resp, err := c.makeRequest("GET", endpoint, params)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch product: %v", err)
	}

	var product map[string]interface{}
	if msg, ok := resp.Message.(map[string]interface{}); ok {
		if data, ok := msg["data"].([]interface{}); ok && len(data) > 0 {
			if prod, ok := data[0].(map[string]interface{}); ok {
				product = prod
			}
		}
	}

	if product == nil {
		return nil, fmt.Errorf("product not found")
	}

	// Get the item_code which we'll need for subsequent queries
	itemCode, _ := product["item_code"].(string)
	if itemCode == "" {
		itemCode = productID // fallback to ID if no item_code
	}

	// Get the price
	priceEndpoint := "/resource/Item Price"
	priceParams := url.Values{}
	priceParams.Add("fields", `["price_list_rate"]`)
	priceParams.Add("filters", fmt.Sprintf(`[["item_code", "=", "%s"], ["selling", "=", 1]]`, itemCode))

	priceResp, err := c.makeRequest("GET", priceEndpoint, priceParams)
	if err != nil {
		log.Printf("Warning: failed to fetch price: %v", err)
	} else {
		if msg, ok := priceResp.Message.(map[string]interface{}); ok {
			if data, ok := msg["data"].([]interface{}); ok && len(data) > 0 {
				if price, ok := data[0].(map[string]interface{}); ok {
					if rate, ok := price["price_list_rate"].(float64); ok {
						product["price_list_rate"] = rate
					}
				}
			}
		}
	}

	// Get stock quantity
	binEndpoint := "/resource/Bin"
	binParams := url.Values{}
	binParams.Add("fields", `["actual_qty"]`)
	binParams.Add("filters", fmt.Sprintf(`[["item_code", "=", "%s"]]`, itemCode))

	binResp, err := c.makeRequest("GET", binEndpoint, binParams)
	if err != nil {
		log.Printf("Warning: failed to fetch stock quantity: %v", err)
	} else {
		if msg, ok := binResp.Message.(map[string]interface{}); ok {
			if data, ok := msg["data"].([]interface{}); ok && len(data) > 0 {
				if bin, ok := data[0].(map[string]interface{}); ok {
					if qty, ok := bin["actual_qty"].(float64); ok {
						product["actual_qty"] = qty
					}
				}
			}
		}
	}

	return product, nil
}

// makeRequest makes an HTTP request to ERPNext API
func (c *ERPNextClient) makeRequest(method, endpoint string, params url.Values) (*ERPNextResponse, error) {
	fullURL := c.BaseURL + endpoint
	if len(params) > 0 {
		fullURL += "?" + params.Encode()
	}

	req, err := http.NewRequest(method, fullURL, nil)
	if err != nil {
		return nil, err
	}

	// Add authentication headers
	req.Header.Set("Authorization", fmt.Sprintf("token %s:%s", c.APIKey, c.APISecret))
	req.Header.Set("Content-Type", "application/json")

	// Debug request
	log.Printf("Making request to: %s", fullURL)

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// Debug response
	log.Printf("Response status: %d", resp.StatusCode)
	log.Printf("Response body: %s", string(body))

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("ERPNext API returned status code %d: %s", resp.StatusCode, string(body))
	}

	// First try to unmarshal into a map to handle different response formats
	var rawResp map[string]interface{}
	if err := json.Unmarshal(body, &rawResp); err != nil {
		return nil, fmt.Errorf("failed to parse response: %v", err)
	}

	// Convert the raw response into our ERPNextResponse format
	result := &ERPNextResponse{
		Message: rawResp,
	}

	return result, nil
}
