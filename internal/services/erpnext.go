package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"sync"
	"time"
)

type ERPNextClient struct {
	BaseURL    string
	APIKey     string
	APISecret  string
	httpClient *http.Client
}

type ERPNextResponse struct {
	Message map[string]interface{} `json:"message"`
	Data    interface{}            `json:"data"`
}

// Get returns a value from the response data by key
func (r *ERPNextResponse) Get(key string) interface{} {
	if r.Message == nil {
		return nil
	}
	if data, ok := r.Message["data"].(map[string]interface{}); ok {
		return data[key]
	}
	return r.Message[key]
}

type ERPNextUser struct {
	ID       string
	FullName string
	Mobile   string
	Email    string
}

func NewERPNextClient(baseURL, apiKey, apiSecret string) (*ERPNextClient, error) {
	if baseURL == "" || apiKey == "" || apiSecret == "" {
		return nil, fmt.Errorf("ERPNext configuration missing. Please provide baseURL, apiKey, and apiSecret")
	}

	if !strings.HasSuffix(baseURL, "/api") {
		baseURL = strings.TrimSuffix(baseURL, "/") + "/api"
	}

	return &ERPNextClient{
		BaseURL:   baseURL,
		APIKey:    apiKey,
		APISecret: apiSecret,
		httpClient: &http.Client{
			Transport: &http.Transport{
				MaxIdleConns:        100,
				IdleConnTimeout:     90 * time.Second,
				TLSHandshakeTimeout: 10 * time.Second,
			},
			Timeout: 15 * time.Second,
		},
	}, nil
}

func (c *ERPNextClient) GetCategories() ([]map[string]interface{}, error) {
	endpoint := "/resource/Item Group"
	params := url.Values{}
	params.Add("fields", `["name", "parent_item_group", "is_group"]`)

	resp, err := c.makeRequest("GET", endpoint, params, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch categories: %v", err)
	}

	if resp.Message == nil {
		return nil, fmt.Errorf("categories not found")
	}

	data, ok := resp.Message["data"].([]interface{})
	if !ok || len(data) == 0 {
		return nil, fmt.Errorf("categories not found")
	}

	result := make([]map[string]interface{}, 0)
	for _, category := range data {
		categoryData, ok := category.(map[string]interface{})
		if !ok {
			continue
		}
		result = append(result, categoryData)
	}

	return result, nil
}

func (c *ERPNextClient) GetCategoriesForWarehouse(warehouse string) ([]map[string]interface{}, error) {
	// First get items from Bin that have stock in the specified warehouse
	endpoint := "/resource/Bin"
	params := url.Values{}

	filters := []string{
		`["actual_qty", ">", "0"]`, // Only items with stock
	}

	if warehouse != "" {
		filters = append(filters, fmt.Sprintf(`["warehouse", "=", "%s"]`, warehouse))
	}

	params.Add("filters", fmt.Sprintf(`[%s]`, strings.Join(filters, ",")))
	params.Add("fields", `["item_code"]`)
	params.Add("limit_page_length", "10000") // Add this line to handle large datasets

	resp, err := c.makeRequest("GET", endpoint, params, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch items from warehouse: %w", err)
	}

	// Extract item codes from bins
	itemCodes := make([]string, 0)
	if resp.Message != nil {
		if data, ok := resp.Message["data"].([]interface{}); ok {
			for _, bin := range data {
				if binData, ok := bin.(map[string]interface{}); ok {
					if itemCode, ok := binData["item_code"].(string); ok {
						itemCodes = append(itemCodes, itemCode)
					}
				}
			}
		}
	}

	if len(itemCodes) == 0 {
		return []map[string]interface{}{}, nil
	}

	// Now get the categories for these items
	endpoint = "/resource/Item"
	params = url.Values{}

	// Create IN filter for item codes
	itemCodesStr := make([]string, len(itemCodes))
	for i, code := range itemCodes {
		itemCodesStr[i] = fmt.Sprintf(`"%s"`, code)
	}

	filters = []string{
		`["disabled", "=", 0]`,
		`["is_sales_item", "=", 1]`,
		fmt.Sprintf(`["name", "in", [%s]]`, strings.Join(itemCodesStr, ",")),
	}

	params.Add("filters", fmt.Sprintf(`[%s]`, strings.Join(filters, ",")))
	params.Add("fields", `["item_group", "name", "item_name"]`)
	params.Add("limit_page_length", "10000") // Add this line to handle large datasets

	resp, err = c.makeRequest("GET", endpoint, params, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch categories: %w", err)
	}

	// Get unique categories
	categories := make(map[string]map[string]interface{})
	if resp.Message != nil {
		if data, ok := resp.Message["data"].([]interface{}); ok {
			for _, item := range data {
				if itemData, ok := item.(map[string]interface{}); ok {
					if itemGroup, ok := itemData["item_group"].(string); ok {
						categories[itemGroup] = map[string]interface{}{
							"name":       itemGroup,
							"item_group": itemGroup,
							"is_group":   true,
						}
					}
				}
			}
		}
	}

	// Convert map to slice
	result := make([]map[string]interface{}, 0, len(categories))
	for _, category := range categories {
		result = append(result, category)
	}

	return result, nil
}

func (c *ERPNextClient) GetProductsByCategory(categoryID string) ([]map[string]interface{}, error) {
	if categoryID == "" {
		return nil, fmt.Errorf("categoryID cannot be empty")
	}

	endpoint := "/resource/Item"
	params := url.Values{}
	params.Add("fields", `["name", "item_name", "item_code", "item_group", "stock_uom", "weight_per_unit", "brand", "image", "disabled"]`)
	params.Add("filters", fmt.Sprintf(`[["item_group", "=", "%s"], ["disabled", "=", 0], ["is_sales_item", "=", 1]]`, categoryID)) // Added is_sales_item filter

	resp, err := c.makeRequest("GET", endpoint, params, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch products: %w", err)
	}

	if resp.Message == nil {
		return nil, fmt.Errorf("invalid response: message field is nil")
	}

	data, ok := resp.Message["data"].([]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid response format: expected array of products")
	}

	var wg sync.WaitGroup
	resultChan := make(chan map[string]interface{}, len(data))
	errorChan := make(chan error, len(data))

	// Process products in parallel
	for _, product := range data {
		wg.Add(1)
		go func(p interface{}) {
			defer wg.Done()

			productData, ok := p.(map[string]interface{})
			if !ok {
				errorChan <- fmt.Errorf("invalid product data format")
				return
			}

			itemCode, _ := productData["item_code"].(string)
			if itemCode == "" {
				errorChan <- fmt.Errorf("missing item_code")
				return
			}

			// Get price
			priceParams := url.Values{}
			priceParams.Add("fields", `["price_list_rate"]`)
			priceParams.Add("filters", fmt.Sprintf(`[["item_code", "=", "%s"], ["selling", "=", 1]]`, itemCode)) // Use selling=1 for Item Price
			priceParams.Add("order_by", "valid_from desc")
			priceParams.Add("limit_page_length", "1")

			if priceResp, err := c.makeRequest("GET", "/resource/Item Price", priceParams, nil); err == nil {
				if priceData, ok := priceResp.Message["data"].([]interface{}); ok && len(priceData) > 0 {
					if price, ok := priceData[0].(map[string]interface{}); ok {
						productData["price_list_rate"] = price["price_list_rate"]
					}
				}
			}

			// Get stock quantity
			stockParams := url.Values{}
			stockParams.Add("fields", `["actual_qty"]`)
			stockParams.Add("filters", fmt.Sprintf(`[["item_code", "=", "%s"]]`, itemCode))

			if stockResp, err := c.makeRequest("GET", "/resource/Bin", stockParams, nil); err == nil {
				if stockData, ok := stockResp.Message["data"].([]interface{}); ok {
					var totalQty float64
					for _, bin := range stockData {
						if binData, ok := bin.(map[string]interface{}); ok {
							if qty, ok := binData["actual_qty"].(float64); ok {
								totalQty += qty
							}
						}
					}
					productData["actual_qty"] = totalQty
				}
			}

			resultChan <- productData
		}(product)
	}

	// Close channels when all goroutines are done
	go func() {
		wg.Wait()
		close(resultChan)
		close(errorChan)
	}()

	// Collect results and handle errors
	var result []map[string]interface{}
	var errors []error

	for i := 0; i < len(data); i++ {
		select {
		case product := <-resultChan:
			if product != nil {
				result = append(result, product)
			}
		case err := <-errorChan:
			if err != nil {
				errors = append(errors, err)
			}
		}
	}

	if len(errors) > 0 {
		return result, fmt.Errorf("encountered %d errors while processing products", len(errors))
	}

	return result, nil
}

func (c *ERPNextClient) GetProductsByCategoryAndWarehouse(categoryID, warehouse string) ([]map[string]interface{}, error) {
	if categoryID == "" {
		return nil, fmt.Errorf("categoryID cannot be empty")
	}

	// First get items in the category
	endpoint := "/resource/Item"
	params := url.Values{}
	filters := []string{
		fmt.Sprintf(`["item_group", "=", "%s"]`, categoryID),
		`["disabled", "=", 0]`,      // Only enabled items
		`["is_sales_item", "=", 1]`, // Only sales items
		`["has_variants", "=", 0]`,  // Exclude template items
	}

	params.Add("filters", fmt.Sprintf(`[%s]`, strings.Join(filters, ",")))
	params.Add("fields", `["name", "item_name", "item_code", "item_group"]`)
	params.Add("limit_page_length", "10000")

	resp, err := c.makeRequest("GET", endpoint, params, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch products: %w", err)
	}

	data, ok := resp.Message["data"].([]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid response format: expected array of products")
	}

	var wg sync.WaitGroup
	resultChan := make(chan map[string]interface{}, len(data))
	errorChan := make(chan error, len(data))

	// Process products in parallel
	for _, product := range data {
		wg.Add(1)
		go func(p interface{}) {
			defer wg.Done()

			productData, ok := p.(map[string]interface{})
			if !ok {
				errorChan <- fmt.Errorf("invalid product data format")
				return
			}

			itemCode, _ := productData["item_code"].(string)
			if itemCode == "" {
				errorChan <- fmt.Errorf("missing item_code")
				return
			}

			// Get warehouse-specific stock quantity
			stockParams := url.Values{}
			stockParams.Add("fields", `["actual_qty"]`)
			stockParams.Add("filters", fmt.Sprintf(`[["item_code", "=", "%s"], ["warehouse", "=", "%s"]]`, itemCode, warehouse))

			if stockResp, err := c.makeRequest("GET", "/resource/Bin", stockParams, nil); err == nil {
				if stockData, ok := stockResp.Message["data"].([]interface{}); ok {
					var totalQty float64
					for _, bin := range stockData {
						if binData, ok := bin.(map[string]interface{}); ok {
							if qty, ok := binData["actual_qty"].(float64); ok {
								totalQty += qty
							}
						}
					}
					productData["actual_qty"] = totalQty
				}
			}

			// Get price (simplified query without warehouse filter)
			priceParams := url.Values{}
			priceParams.Add("fields", `["price_list_rate"]`)
			priceParams.Add("filters", fmt.Sprintf(`[["item_code", "=", "%s"], ["selling", "=", 1]]`, itemCode))
			priceParams.Add("order_by", "valid_from desc")
			priceParams.Add("limit_page_length", "1")

			if priceResp, err := c.makeRequest("GET", "/resource/Item Price", priceParams, nil); err == nil {
				if priceData, ok := priceResp.Message["data"].([]interface{}); ok && len(priceData) > 0 {
					if price, ok := priceData[0].(map[string]interface{}); ok {
						if rate, ok := price["price_list_rate"].(float64); ok {
							productData["price_list_rate"] = rate
						}
					}
				}
			}

			// Only include products that have stock in the specified warehouse
			if qty, ok := productData["actual_qty"].(float64); ok && qty > 0 {
				resultChan <- productData
			}
		}(product)
	}

	// Wait for all goroutines to complete
	go func() {
		wg.Wait()
		close(resultChan)
		close(errorChan)
	}()

	// Collect results and errors
	var result []map[string]interface{}
	var errors []error

	// Read from both channels until resultChan is closed
	for {
		select {
		case product, ok := <-resultChan:
			if !ok {
				// resultChan is closed, we're done
				if len(errors) > 0 {
					return result, fmt.Errorf("encountered %d errors while processing products", len(errors))
				}
				return result, nil
			}
			result = append(result, product)
		case err := <-errorChan:
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
}

func (c *ERPNextClient) SearchProductsByCategory(categoryID string, searchTerm string, warehouse string) ([]map[string]interface{}, error) {
	if categoryID == "" {
		return nil, fmt.Errorf("categoryID cannot be empty")
	}

	endpoint := "/resource/Bin"
	params := url.Values{}

	// Get items with stock in the specified warehouse
	filters := []string{
		fmt.Sprintf(`["item_group", "=", "%s"]`, categoryID),
		`["actual_qty", ">", "0"]`, // Only items with stock
	}

	if warehouse != "" {
		filters = append(filters, fmt.Sprintf(`["warehouse", "=", "%s"]`, warehouse))
	}

	params.Add("filters", fmt.Sprintf(`[%s]`, strings.Join(filters, ",")))
	params.Add("fields", `["item_code", "actual_qty", "warehouse"]`)

	resp, err := c.makeRequest("GET", endpoint, params, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch products: %w", err)
	}

	if resp.Message == nil {
		return nil, fmt.Errorf("invalid response: message field is nil")
	}

	// Extract item codes and quantities from bins
	itemQuantities := make(map[string]float64)
	if data, ok := resp.Message["data"].([]interface{}); ok {
		for _, bin := range data {
			if binData, ok := bin.(map[string]interface{}); ok {
				itemCode, _ := binData["item_code"].(string)
				qty, _ := binData["actual_qty"].(float64)
				if itemCode != "" {
					itemQuantities[itemCode] += qty
				}
			}
		}
	}

	if len(itemQuantities) == 0 {
		return []map[string]interface{}{}, nil
	}

	// Get item details for the items with stock
	itemCodes := make([]string, 0, len(itemQuantities))
	for code := range itemQuantities {
		itemCodes = append(itemCodes, code)
	}

	// Create IN filter for item codes
	itemCodesStr := make([]string, len(itemCodes))
	for i, code := range itemCodes {
		itemCodesStr[i] = fmt.Sprintf(`"%s"`, code)
	}

	// Get full item details
	endpoint = "/resource/Item"
	params = url.Values{}
	itemFilters := []string{
		fmt.Sprintf(`["name", "in", [%s]]`, strings.Join(itemCodesStr, ",")),
		`["disabled", "=", 0]`,
		`["is_sales_item", "=", 1]`,
	}

	params.Add("filters", fmt.Sprintf(`[%s]`, strings.Join(itemFilters, ",")))
	params.Add("fields", `["name", "item_name", "item_code", "item_group", "stock_uom", "weight_per_unit", "brand", "image"]`)

	resp, err = c.makeRequest("GET", endpoint, params, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch item details: %w", err)
	}

	var result []map[string]interface{}
	if resp.Message != nil {
		if data, ok := resp.Message["data"].([]interface{}); ok {
			for _, item := range data {
				if itemData, ok := item.(map[string]interface{}); ok {
					itemCode, _ := itemData["name"].(string)
					if qty, exists := itemQuantities[itemCode]; exists {
						// Add quantity to item data
						itemData["actual_qty"] = qty

						// Get price for the item
						priceParams := url.Values{}
						priceParams.Add("fields", `["price_list_rate"]`)
						priceParams.Add("filters", fmt.Sprintf(`[["item_code", "=", "%s"], ["selling", "=", 1]]`, itemCode))
						priceParams.Add("order_by", "valid_from desc")
						priceParams.Add("limit_page_length", "1")

						if priceResp, err := c.makeRequest("GET", "/resource/Item Price", priceParams, nil); err == nil {
							if priceData, ok := priceResp.Message["data"].([]interface{}); ok && len(priceData) > 0 {
								if price, ok := priceData[0].(map[string]interface{}); ok {
									itemData["price_list_rate"] = price["price_list_rate"]
								}
							}
						}

						result = append(result, itemData)
					}
				}
			}
		}
	}

	return result, nil
}

func (c *ERPNextClient) GetProduct(productID string, warehouse string) (map[string]interface{}, error) {
	// Get basic product info
	endpoint := "/resource/Item"
	params := url.Values{}
	params.Add("filters", fmt.Sprintf(`[["name", "=", "%s"]]`, productID))
	params.Add("fields", `["name", "item_name", "item_code", "item_group"]`)

	resp, err := c.makeRequest("GET", endpoint, params, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch product: %w", err)
	}

	data, ok := resp.Message["data"].([]interface{})
	if !ok || len(data) == 0 {
		return nil, fmt.Errorf("product not found")
	}

	productData, ok := data[0].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid product data format")
	}

	// Get price
	priceParams := url.Values{}
	priceParams.Add("fields", `["price_list_rate"]`)
	priceParams.Add("filters", fmt.Sprintf(`[["item_code", "=", "%s"], ["selling", "=", 1]]`, productID))
	priceParams.Add("order_by", "valid_from desc")
	priceParams.Add("limit_page_length", "1")

	if priceResp, err := c.makeRequest("GET", "/resource/Item Price", priceParams, nil); err == nil {
		if priceData, ok := priceResp.Message["data"].([]interface{}); ok && len(priceData) > 0 {
			if price, ok := priceData[0].(map[string]interface{}); ok {
				if rate, ok := price["price_list_rate"].(float64); ok {
					productData["price_list_rate"] = rate
				}
			}
		}
	}

	// Get stock quantity for specific warehouse
	stockParams := url.Values{}
	stockParams.Add("fields", `["actual_qty"]`)
	stockParams.Add("filters", fmt.Sprintf(`[["item_code", "=", "%s"], ["warehouse", "=", "%s"]]`, productID, warehouse))

	if stockResp, err := c.makeRequest("GET", "/resource/Bin", stockParams, nil); err == nil {
		if stockData, ok := stockResp.Message["data"].([]interface{}); ok {
			var totalQty float64
			for _, bin := range stockData {
				if binData, ok := bin.(map[string]interface{}); ok {
					if qty, ok := binData["actual_qty"].(float64); ok {
						totalQty += qty
					}
				}
			}
			productData["actual_qty"] = totalQty
		}
	}

	return productData, nil
}

func (c *ERPNextClient) CreateDocument(doc map[string]interface{}) (*ERPNextResponse, error) {
	endpoint := "/resource/" + url.PathEscape(doc["doctype"].(string))
	return c.makeRequest("POST", endpoint, nil, doc)
}

func (c *ERPNextClient) GetUserByPhone(phone string) (*ERPNextUser, error) {
	phone = strings.TrimPrefix(phone, "+")

	endpoint := "/resource/User"

	params := url.Values{}
	params.Add("filters", fmt.Sprintf(`[["mobile_no","=","%s"]]`, phone))
	params.Add("fields", `["name","full_name","mobile_no","email"]`)
	params.Add("limit_page_length", "1")

	resp, err := c.makeRequest("GET", endpoint, params, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch user: %v", err)
	}

	if resp.Message == nil {
		return nil, fmt.Errorf("user not found")
	}

	data, ok := resp.Message["data"].([]interface{})
	if !ok || len(data) == 0 {
		return nil, fmt.Errorf("user not found")
	}

	userData, ok := data[0].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid user data format")
	}

	name, _ := userData["name"].(string)
	fullName, _ := userData["full_name"].(string)
	mobile, _ := userData["mobile_no"].(string)
	email, _ := userData["email"].(string)
	if name == "" || mobile == "" {
		return nil, fmt.Errorf("invalid user data: missing required fields")
	}

	return &ERPNextUser{
		ID:       name,
		FullName: fullName,
		Mobile:   mobile,
		Email:    email,
	}, nil
}

// SystemCategoryPatterns defines patterns for identifying system categories
const (
	SystemCategoryPrefix   = "Raw "
	SystemCategorySuffixes = "Fees|Material|Services|Assemblies|Deductions|Consumable"
)

// ExcludedCategories contains the list of categories to be excluded
var ExcludedCategories = []string{
	"All Item Groups", // Root category
}

// buildSystemCategoryFilters creates filters to exclude system categories
// func buildSystemCategoryFilters() []string {
// 	filters := []string{
// 		`["disabled", "=", 0]`,
// 		`["is_group", "in", [0, 1]]`,
// 		fmt.Sprintf(`["name", "not like", "%s%%"]`, SystemCategoryPrefix),
// 	}

// 	// Add suffix-based filters
// 	for _, suffix := range strings.Split(SystemCategorySuffixes, "|") {
// 		filters = append(filters, fmt.Sprintf(`["name", "not like", "%% %s"]`, suffix))
// 	}

// 	// Add exact match exclusions
// 	for _, category := range ExcludedCategories {
// 		filters = append(filters, fmt.Sprintf(`["name", "!=", "%s"]`, category))
// 	}

// 	return filters
// }

func (c *ERPNextClient) IsUserEnabled(username string) (bool, error) {
	if username == "" {
		return false, fmt.Errorf("username cannot be empty")
	}

	endpoint := "/resource/User"
	params := url.Values{}

	// Add essential fields for user status checking
	params.Add("fields", `["name", "enabled", "user_type"]`)
	params.Add("filters", fmt.Sprintf(`[["name", "=", "%s"]]`, username))

	resp, err := c.makeRequest("GET", endpoint, params, nil)
	if err != nil {
		return false, fmt.Errorf("failed to fetch user status: %w", err)
	}

	if resp.Message == nil {
		return false, fmt.Errorf("invalid response: message field is nil")
	}

	data, ok := resp.Message["data"].([]interface{})
	if !ok || len(data) == 0 {
		return false, fmt.Errorf("user not found")
	}

	userData, ok := data[0].(map[string]interface{})
	if !ok {
		return false, fmt.Errorf("invalid user data format")
	}

	// Check if user is enabled
	enabled, ok := userData["enabled"].(float64)
	if !ok {
		return false, fmt.Errorf("invalid enabled status format")
	}

	return enabled == 1, nil
}

func (c *ERPNextClient) makeRequest(method, endpoint string, params url.Values, body interface{}) (*ERPNextResponse, error) {
	fullURL := c.BaseURL + endpoint
	if len(params) > 0 {
		fullURL += "?" + params.Encode()
	}

	var reqBody io.Reader
	if body != nil {
		jsonData, err := json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request body: %v", err)
		}
		reqBody = bytes.NewBuffer(jsonData)
	}

	req, err := http.NewRequest(method, fullURL, reqBody)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Authorization", fmt.Sprintf("token %s:%s", c.APIKey, c.APISecret))
	req.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("ERPNext API returned status code %d: %s", resp.StatusCode, string(responseBody))
	}

	var rawResp map[string]interface{}
	if err := json.Unmarshal(responseBody, &rawResp); err != nil {
		return nil, fmt.Errorf("failed to parse response: %v", err)
	}

	return &ERPNextResponse{Message: rawResp}, nil
}

func (c *ERPNextClient) SearchProducts(searchTerm string) ([]map[string]interface{}, error) {
	if searchTerm == "" {
		return nil, fmt.Errorf("searchTerm cannot be empty")
	}

	endpoint := "/resource/Item"
	params := url.Values{}

	// Basic fields
	params.Add("fields", `["name", "item_name", "item_code", "item_group", "stock_uom", "weight_per_unit", "brand", "image", "disabled"]`)

	// Build search filter
	var filters []string
	filters = append(filters, `["disabled", "=", 0]`)
	filters = append(filters, `["is_sales_item", "=", 1]`) // Filter sales items at Item endpoint
	filters = append(filters, fmt.Sprintf(`["item_name", "like", "%%%s%%"]`, searchTerm))

	params.Add("filters", fmt.Sprintf(`[%s]`, strings.Join(filters, ",")))

	resp, err := c.makeRequest("GET", endpoint, params, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to search products: %w", err)
	}

	if resp.Message == nil {
		return nil, fmt.Errorf("invalid response: message field is nil")
	}

	data, ok := resp.Message["data"].([]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid response format: expected array of products")
	}

	var wg sync.WaitGroup
	resultChan := make(chan map[string]interface{}, len(data))
	errorChan := make(chan error, len(data))

	for _, product := range data {
		wg.Add(1)
		go func(p interface{}) {
			defer wg.Done()

			productData, ok := p.(map[string]interface{})
			if !ok {
				errorChan <- fmt.Errorf("invalid product data format")
				return
			}

			itemCode, _ := productData["item_code"].(string)
			if itemCode == "" {
				errorChan <- fmt.Errorf("missing item_code")
				return
			}

			// Get price from Item Price API
			priceParams := url.Values{}
			priceParams.Add("fields", `["price_list_rate"]`)
			priceParams.Add("filters", fmt.Sprintf(`[["item_code", "=", "%s"], ["selling", "=", 1]]`, itemCode)) // Use selling=1 for Item Price endpoint
			priceParams.Add("order_by", "valid_from desc")
			priceParams.Add("limit_page_length", "1")

			if priceResp, err := c.makeRequest("GET", "/resource/Item Price", priceParams, nil); err == nil {
				if priceData, ok := priceResp.Message["data"].([]interface{}); ok && len(priceData) > 0 {
					if price, ok := priceData[0].(map[string]interface{}); ok {
						productData["price_list_rate"] = price["price_list_rate"]
					}
				}
			}

			// Get stock quantity from Bin API
			stockParams := url.Values{}
			stockParams.Add("fields", `["actual_qty"]`)
			stockParams.Add("filters", fmt.Sprintf(`[["item_code", "=", "%s"]]`, itemCode))

			if stockResp, err := c.makeRequest("GET", "/resource/Bin", stockParams, nil); err == nil {
				if stockData, ok := stockResp.Message["data"].([]interface{}); ok {
					var totalQty float64
					for _, bin := range stockData {
						if binData, ok := bin.(map[string]interface{}); ok {
							if qty, ok := binData["actual_qty"].(float64); ok {
								totalQty += qty
							}
						}
					}
					productData["actual_qty"] = totalQty
				}
			}

			resultChan <- productData
		}(product)
	}

	// Close channels when all goroutines are done
	go func() {
		wg.Wait()
		close(resultChan)
		close(errorChan)
	}()

	// Collect results and handle errors
	var result []map[string]interface{}
	var errors []error

	for i := 0; i < len(data); i++ {
		select {
		case product := <-resultChan:
			if product != nil {
				result = append(result, product)
			}
		case err := <-errorChan:
			if err != nil {
				errors = append(errors, err)
			}
		}
	}

	if len(errors) > 0 {
		return result, fmt.Errorf("encountered %d errors while processing products", len(errors))
	}

	return result, nil
}

// CheckWarehouseExists verifies if a warehouse exists in ERPNext
func (c *ERPNextClient) CheckWarehouseExists(warehouse string) (bool, error) {
	endpoint := "/resource/Warehouse"
	params := url.Values{}
	params.Add("filters", fmt.Sprintf(`[["name", "=", "%s"]]`, warehouse))
	params.Add("fields", `["name"]`)
	params.Add("limit_page_length", "1")

	resp, err := c.makeRequest("GET", endpoint, params, nil)
	if err != nil {
		return false, fmt.Errorf("failed to check warehouse: %w", err)
	}

	if resp.Message == nil {
		return false, nil
	}

	data, ok := resp.Message["data"].([]interface{})
	if !ok {
		return false, nil
	}

	return len(data) > 0, nil
}

// GetWarehouses returns all warehouses from ERPNext
func (c *ERPNextClient) GetWarehouses() ([]map[string]interface{}, error) {
	endpoint := "/resource/Warehouse"
	params := url.Values{}
	params.Add("fields", `["name", "warehouse_name", "is_group", "company"]`)

	resp, err := c.makeRequest("GET", endpoint, params, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch warehouses: %w", err)
	}

	if resp.Message == nil {
		return nil, fmt.Errorf("invalid response: message field is nil")
	}

	data, ok := resp.Message["data"].([]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid response format")
	}

	result := make([]map[string]interface{}, 0)
	for _, warehouse := range data {
		if warehouseData, ok := warehouse.(map[string]interface{}); ok {
			// Skip group warehouses if they exist
			if isGroup, ok := warehouseData["is_group"].(bool); ok && isGroup {
				continue
			}
			result = append(result, warehouseData)
		}
	}

	return result, nil
}

func (c *ERPNextClient) GetDocument(doctype, name string) (*ERPNextResponse, error) {
	endpoint := fmt.Sprintf("/resource/%s/%s", url.PathEscape(doctype), url.PathEscape(name))
	return c.makeRequest("GET", endpoint, nil, nil)
}

// SubmitDocument submits a document in ERPNext by making a POST request to the submit endpoint
func (c *ERPNextClient) SubmitDocument(doctype, name string) error {
	endpoint := fmt.Sprintf("/resource/%s/%s/submit", url.PathEscape(doctype), url.PathEscape(name))
	_, err := c.makeRequest("POST", endpoint, nil, nil)
	if err != nil {
		return fmt.Errorf("failed to submit document %s %s: %w", doctype, name, err)
	}
	return nil
}

// SubmitDocumentMethod submits a document in ERPNext by making a POST request to the submit endpoint
func (c *ERPNextClient) SubmitDocumentMethod(doctype, name string) error {
	// Try the direct submit endpoint first
	if err := c.SubmitDocument(doctype, name); err == nil {
		return nil
	}

	// If direct submit fails, try the method-based submission
	// First, fetch the latest document to get its current state
	latestDoc, err := c.GetDocument(doctype, name)
	if err != nil {
		return fmt.Errorf("failed to fetch latest document: %w", err)
	}

	docData, ok := latestDoc.Message["data"].(map[string]interface{})
	if !ok {
		return fmt.Errorf("invalid document data format")
	}

	// Create the document structure with all the existing fields
	doc := docData
	doc["doctype"] = doctype
	doc["name"] = name
	doc["__islocal"] = 0
	doc["docstatus"] = 0 // 0 = Draft, 1 = Submitted, 2 = Cancelled

	// Create the payload with the correct structure including the required 'action' parameter
	docPayload := map[string]interface{}{
		"doc":    doc,
		"action": "Submit",
	}

	// Convert the doc to JSON string as expected by the API
	docJSON, err := json.Marshal(docPayload["doc"])
	if err != nil {
		return fmt.Errorf("failed to marshal document: %w", err)
	}

	// Create the final payload with doc as JSON string
	payload := map[string]interface{}{
		"doc":    string(docJSON),
		"action": "Submit",
	}

	// Define the endpoint for the method-based submission
	endpoint := "/method/frappe.desk.form.save.savedocs"

	// Make the request with the proper payload
	resp, err := c.makeRequest("POST", endpoint, nil, payload)
	if err != nil {
		return fmt.Errorf("failed to submit document %s %s: %w", doctype, name, err)
	}

	// Check for errors in the response
	if resp.Message == nil {
		return fmt.Errorf("invalid response while submitting document %s %s", doctype, name)
	}

	return nil
}

// DeleteDocument deletes a document in ERPNext
func (c *ERPNextClient) DeleteDocument(doctype, name string) error {
	if doctype == "" || name == "" {
		return fmt.Errorf("doctype and name are required")
	}

	endpoint := fmt.Sprintf("/resource/%s/%s", url.PathEscape(doctype), url.PathEscape(name))

	resp, err := c.makeRequest("DELETE", endpoint, nil, nil)
	if err != nil {
		return fmt.Errorf("failed to delete document %s %s: %w", doctype, name, err)
	}

	// Check if deletion was successful
	if resp.Message == nil {
		return fmt.Errorf("invalid response while deleting document %s %s", doctype, name)
	}

	// Some ERPNext versions return a success message
	if msg, ok := resp.Message["message"].(string); ok && strings.Contains(strings.ToLower(msg), "error") {
		return fmt.Errorf("failed to delete document: %s", msg)
	}

	return nil
}

// CustomerDetails represents customer information from ERPNext
type CustomerDetails struct {
	ID           string  `json:"name"`
	CustomerName string  `json:"customer_name"`
	CreditLimit  float64 `json:"credit_limit"`
	Balance      float64 `json:"balance"`
	IsEnabled    bool    `json:"enabled"`
}

// HasSufficientCredit checks if customer has enough credit for the transaction
func (c *CustomerDetails) HasSufficientCredit(amount float64) bool {
	availableCredit := c.CreditLimit - c.Balance
	return availableCredit >= amount
}

// GetCustomerDetails fetches customer information from ERPNext
func (c *ERPNextClient) GetCustomerDetails(customerCode string) (*CustomerDetails, error) {
	endpoint := fmt.Sprintf("/resource/Customer/%s", url.PathEscape(customerCode))

	params := url.Values{}
	params.Add("fields", `["name", "customer_name", "credit_limit", "enabled"]`)

	resp, err := c.makeRequest("GET", endpoint, params, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch customer details: %w", err)
	}

	if resp.Message == nil {
		return nil, fmt.Errorf("customer not found: %s", customerCode)
	}

	data, ok := resp.Message["data"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid customer data format")
	}

	// Extract credit limit
	var creditLimit float64
	if cl, ok := data["credit_limit"].(float64); ok {
		creditLimit = cl
	}

	// Get customer's outstanding balance
	balanceEndpoint := "/api/method/erpnext.accounts.utils.get_balance_on"
	balanceParams := url.Values{}
	balanceParams.Add("party_type", "Customer")
	balanceParams.Add("party", customerCode)
	balanceParams.Add("date", time.Now().Format("2006-01-02"))

	balanceResp, err := c.makeRequest("GET", balanceEndpoint, balanceParams, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch customer balance: %w", err)
	}

	var balance float64
	if bal, ok := balanceResp.Message["balance"].(float64); ok {
		balance = bal
	}

	customer := &CustomerDetails{
		ID:           data["name"].(string),
		CustomerName: data["customer_name"].(string),
		CreditLimit:  creditLimit,
		Balance:      balance,
		IsEnabled:    data["enabled"].(bool),
	}

	if !customer.IsEnabled {
		return nil, fmt.Errorf("customer account is disabled: %s", customerCode)
	}

	return customer, nil
}

// GetCompanyDefaultAccounts retrieves default accounts for a company
func (c *ERPNextClient) GetCompanyDefaultAccounts(company string) (map[string]string, error) {
	endpoint := "/method/erpnext.accounts.doctype.account.account.get_default_company_account"
	params := url.Values{}
	params.Add("company", company)

	resp, err := c.makeRequest("GET", endpoint, params, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get company accounts: %w", err)
	}

	if resp.Message == nil {
		return nil, fmt.Errorf("invalid response: message field is nil")
	}

	accounts := make(map[string]string)
	if data, ok := resp.Message["data"].(map[string]interface{}); ok {
		if defaultAccount, ok := data["default_account"].(string); ok {
			accounts["default"] = defaultAccount
		}
		if incomeAccount, ok := data["default_income_account"].(string); ok {
			accounts["income"] = incomeAccount
		}
		if receivableAccount, ok := data["default_receivable_account"].(string); ok {
			accounts["receivable"] = receivableAccount
		}
	}

	return accounts, nil
}
