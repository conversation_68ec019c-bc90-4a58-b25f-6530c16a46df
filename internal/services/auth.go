package services

import (
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/itunza/telegram-silcore/internal/models"
	"gorm.io/gorm"
)

// AuthService handles user authentication and management
type AuthService struct {
	db            *gorm.DB
	sessionSvc    *SessionService
	erpnextClient *ERPNextClient
}

// NewAuthService creates a new authentication service
func NewAuthService(db *gorm.DB, sessionSvc *SessionService, erpnextClient *ERPNextClient) *AuthService {
	return &AuthService{
		db:            db,
		sessionSvc:    sessionSvc,
		erpnextClient: erpnextClient,
	}
}

// LoginUser authenticates a user with their phone number
func (s *AuthService) LoginUser(telegramID int64, phone string) (*models.User, error) {
	// Normalize phone number - remove + prefix if exists
	phone = strings.TrimPrefix(phone, "+")

	var user models.User

	// First check if user exists in ERPNext
	erpnextUser, err := s.erpnextClient.GetUserByPhone(phone)
	if err != nil {
		if err.Error() == "user not found" {
			return nil, fmt.Errorf("phone number not registered in ERPNext. Please contact support to register")
		}
		return nil, fmt.Errorf("ERPNext error: %v", err)
	}

	// Check if user is enabled in ERPNext
	enabled, err := s.erpnextClient.IsUserEnabled(erpnextUser.ID)
	if err != nil {
		return nil, fmt.Errorf("ERPNext error: %v", err)
	}
	if !enabled {
		return nil, fmt.Errorf("your account is disabled. Please contact support")
	}

	// Try to find existing user in local database
	result := s.db.Where("mobile = ?", phone).First(&user)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			// Create new user since they exist in ERPNext but not locally
			user = models.User{
				TelegramID: telegramID,
				Mobile:     phone,
				ERPNextID:  erpnextUser.ID,
				IsActive:   true,
				LastLogin:  time.Now(),
				CreatedAt:  time.Now(),
				UpdatedAt:  time.Now(),
			}

			if err := s.db.Create(&user).Error; err != nil {
				return nil, fmt.Errorf("failed to create user: %v", err)
			}
		} else {
			return nil, fmt.Errorf("database error: %v", result.Error)
		}
	} else {
		// Update telegram ID if it has changed
		if user.TelegramID != telegramID {
			if err := s.db.Model(&user).Update("telegram_id", telegramID).Error; err != nil {
				return nil, fmt.Errorf("failed to update telegram ID: %v", err)
			}
			user.TelegramID = telegramID
		}
	}

	// Update last login time
	if err := s.db.Model(&user).Update("last_login", time.Now()).Error; err != nil {
		return nil, fmt.Errorf("failed to update last login: %v", err)
	}

	// Clear any existing session first
	if err := s.sessionSvc.ClearSession(telegramID); err != nil {
		return nil, fmt.Errorf("failed to clear existing session: %v", err)
	}

	// Create new session
	session := &SessionData{
		LocalUserID:   telegramID,
		ERPNextUser:   user.ERPNextID,
		IsActive:      true,
		LastActive:    time.Now(),
		SelectedItems: make(map[string]interface{}),
		Customers:     make(map[string]*CustomerSession),
	}

	// Create default customer
	defaultCustomer := &CustomerSession{
		CustomerID: "default",
		Cart:       make(map[string]*CartItem),
		CreatedAt:  time.Now(),
		LastActive: time.Now(),
	}
	session.Customers["default"] = defaultCustomer
	session.ActiveCustomerID = "default"

	// Save session
	if err := s.sessionSvc.SaveSession(session); err != nil {
		return nil, fmt.Errorf("failed to save session: %v", err)
	}

	return &user, nil
}

// LogoutUser logs out a user by clearing their session
func (s *AuthService) LogoutUser(telegramID int64) error {
	// Get current session
	session := s.sessionSvc.GetSession(telegramID)
	if session.IsActive {
		// Update user's last active time in database
		var user models.User
		if err := s.db.First(&user, session.LocalUserID).Error; err == nil {
			s.db.Model(&user).Update("last_active", time.Now())
		}
	}

	return s.sessionSvc.ClearSession(telegramID)
}

// ValidateSession checks if a user's session is valid and active
func (s *AuthService) ValidateSession(telegramID int64) bool {
	// Get session
	session := s.sessionSvc.GetSession(telegramID)
	if session == nil || !session.IsActive {
		// Only log once per minute per user to avoid spam
		if time.Since(session.LastActive) > time.Minute {
			log.Printf("No active session found for user %d", telegramID)
		}
		return false
	}

	// Check if user exists and is active
	var user models.User
	if err := s.db.Where("telegram_id = ?", telegramID).First(&user).Error; err != nil {
		log.Printf("Error finding user %d: %v", telegramID, err)
		s.sessionSvc.ClearSession(telegramID)
		return false
	}

	// Check if user is active
	if !user.IsActive {
		log.Printf("User %d is not active", telegramID)
		s.sessionSvc.ClearSession(telegramID)
		return false
	}

	// Update last active time
	session.LastActive = time.Now()
	if err := s.sessionSvc.SaveSession(session); err != nil {
		log.Printf("Error updating session last active time: %v", err)
		// Don't fail validation just because we couldn't update last active time
	}

	return true
}

// GetUserByID retrieves a user by their ID
func (s *AuthService) GetUserByID(userID uint) (*models.User, error) {
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("database error: %v", err)
	}
	return &user, nil
}

// GetUserByPhone retrieves a user by their phone number
func (s *AuthService) GetUserByPhone(phone string) (*models.User, error) {
	var user models.User

	// Try to find user in local database
	result := s.db.Where("mobile = ?", phone).First(&user)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			// Try to find user in ERPNext
			erpnextUser, err := s.erpnextClient.GetUserByPhone(phone)
			if err != nil {
				return nil, fmt.Errorf("user not found in ERPNext: %v", err)
			}

			// Create new user
			user = models.User{
				Mobile:    phone,
				ERPNextID: erpnextUser.ID,
				IsActive:  true,
				LastLogin: time.Now(),
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			}

			if err := s.db.Create(&user).Error; err != nil {
				return nil, fmt.Errorf("failed to create user: %v", err)
			}
		} else {
			return nil, fmt.Errorf("database error: %v", result.Error)
		}
	}

	return &user, nil
}
