package services

import (
	"context"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/itunza/telegram-silcore/internal/types"
	"github.com/itunza/telegram-silcore/internal/utils"
	"github.com/lib/pq"
	"github.com/redis/go-redis/v9"
	"gopkg.in/telebot.v3"
	"gorm.io/gorm"
)

// Database interface defines the required database operations
type Database interface {
	CreatePayment(ctx context.Context, payment *Payment) error
	UpdatePayment(ctx context.Context, payment *Payment) error
	GetPayment(ctx context.Context, paymentID string) (*Payment, error)
}

// RedisClient wraps redis operations
type RedisClient struct {
	client *redis.Client
}

// NewRedisClient creates a new Redis client
func NewRedisClient(redisURL string) (*RedisClient, error) {
	opt, err := redis.ParseURL(redisURL)
	if err != nil {
		return nil, fmt.Errorf("failed to parse Redis URL: %w", err)
	}

	client := redis.NewClient(opt)

	// Test connection
	ctx := context.Background()
	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	return &RedisClient{client: client}, nil
}

// Set stores a key-value pair with expiration
func (r *RedisClient) Set(ctx context.Context, key string, value string, expiration time.Duration) error {
	return r.client.Set(ctx, key, value, expiration).Err()
}

// Get retrieves a value by key
func (r *RedisClient) Get(ctx context.Context, key string) (string, error) {
	val, err := r.client.Get(ctx, key).Result()
	if err == redis.Nil {
		return "", fmt.Errorf("key not found: %s", key)
	}
	return val, err
}

// GormDatabase implements Database interface using GORM
type GormDatabase struct {
	db *gorm.DB
}

// NewGormDatabase creates a new GormDatabase instance
func NewGormDatabase(db *gorm.DB) *GormDatabase {
	return &GormDatabase{db: db}
}

// CreatePayment implements Database.CreatePayment
func (d *GormDatabase) CreatePayment(ctx context.Context, payment *Payment) error {
	return d.db.WithContext(ctx).Create(payment).Error
}

// UpdatePayment implements Database.UpdatePayment
func (d *GormDatabase) UpdatePayment(ctx context.Context, payment *Payment) error {
	return d.db.WithContext(ctx).Save(payment).Error
}

// GetPayment implements Database.GetPayment
func (d *GormDatabase) GetPayment(ctx context.Context, paymentID string) (*Payment, error) {
	var payment Payment
	if err := d.db.WithContext(ctx).First(&payment, "id = ?", paymentID).Error; err != nil {
		return nil, err
	}
	return &payment, nil
}

type PaymentMethod string

const (
	PaymentMethodMPesa   PaymentMethod = "mpesa"
	PaymentMethodAccount PaymentMethod = "account"
)

type PaymentStatus string

const (
	PaymentStatusPending   PaymentStatus = "pending"
	PaymentStatusComplete  PaymentStatus = "complete"
	PaymentStatusFailed    PaymentStatus = "failed"
	PaymentStatusCancelled PaymentStatus = "cancelled"
)

// Payment represents a payment record in the database
type Payment struct {
	ID           string         `gorm:"primaryKey;type:varchar(255)"`
	UserID       int64          `gorm:"index;not null"`
	OrderRefs    pq.StringArray `gorm:"type:text[];not null"`
	Amount       float64        `gorm:"type:decimal(10,2);not null"`
	Method       PaymentMethod  `gorm:"type:varchar(50);not null"`
	Status       PaymentStatus  `gorm:"type:varchar(50);not null"`
	Warehouse    string         `gorm:"default:'Main Warehouse'"`
	Reference    string         `gorm:"type:varchar(255)"`
	PhoneNumber  string         `gorm:"type:varchar(20)"`
	CustomerCode string         `gorm:"type:varchar(255)"`
	ErrorMessage string         `gorm:"type:text"`
	CreatedAt    time.Time      `gorm:"type:timestamptz;default:CURRENT_TIMESTAMP"`
	CompletedAt  *time.Time     `gorm:"type:timestamptz"`
	MessageID    int            `gorm:"type:int"`
	ChatID       int64          `gorm:"type:bigint"`
}

// TableName specifies the table name for the Payment model
func (Payment) TableName() string {
	return "payments"
}

type PaymentService struct {
	db           *gorm.DB
	erpnext      *ERPNextClient
	mpesa        *MPesaClient
	orderService *OrderService
	redis        *redis.Client
	sessions     *SessionService
	bot          *telebot.Bot
}

func NewPaymentService(
	db *gorm.DB,
	erpnext *ERPNextClient,
	mpesa *MPesaClient,
	orderService *OrderService,
	redisURL string,
	sessions *SessionService,
) (*PaymentService, error) {
	// Parse Redis URL and create client
	opt, err := redis.ParseURL(redisURL)
	if err != nil {
		return nil, fmt.Errorf("invalid Redis URL: %w", err)
	}
	redisClient := redis.NewClient(opt)

	// Test Redis connection
	ctx := context.Background()
	if err := redisClient.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	return &PaymentService{
		db:           db,
		erpnext:      erpnext,
		mpesa:        mpesa,
		orderService: orderService,
		redis:        redisClient,
		sessions:     sessions,
	}, nil
}

// SetBot sets the bot instance for the payment service
func (s *PaymentService) SetBot(bot *telebot.Bot) {
	s.bot = bot
}

// GetBot returns the bot instance
func (s *PaymentService) GetBot() *telebot.Bot {
	return s.bot
}

// InitiatePayment starts the payment process for given orders
func (s *PaymentService) InitiatePayment(ctx context.Context, userID int64, orderRefs []string, method PaymentMethod, customer *types.Customer) (*Payment, error) {
	// Calculate total amount from orders
	var totalAmount float64
	for _, ref := range orderRefs {
		order, err := s.erpnext.GetDocument("Sales Order", ref)
		if err != nil {
			return nil, fmt.Errorf("failed to get order %s: %w", ref, err)
		}
		amount, ok := order.Get("grand_total").(float64)
		if !ok {
			return nil, fmt.Errorf("invalid amount format in order %s", ref)
		}
		totalAmount += amount
	}

	// Create payment record
	payment := &Payment{
		ID:          utils.GenerateUUID(),
		OrderRefs:   orderRefs,
		UserID:      userID,
		Amount:      totalAmount,
		PhoneNumber: customer.Mobile,
		Method:      method,
		Status:      PaymentStatusPending,
		CreatedAt:   time.Now(),
	}

	// Store payment in database using GORM directly
	if err := s.db.WithContext(ctx).Create(payment).Error; err != nil {
		return nil, fmt.Errorf("failed to create payment record: %w", err)
	}

	return payment, nil
}

// ProcessAccountPayment handles payment on account
func (s *PaymentService) ProcessAccountPayment(ctx context.Context, payment *Payment, customerCode string) error {
	payment.CustomerCode = customerCode

	// Verify customer credit limit and balance
	customer, err := s.erpnext.GetCustomerDetails(customerCode)
	if err != nil {
		payment.Status = PaymentStatusFailed
		payment.ErrorMessage = fmt.Sprintf("Failed to verify customer: %v", err)
		if err := s.db.WithContext(ctx).Save(payment).Error; err != nil {
			return fmt.Errorf("failed to update payment status: %w", err)
		}
		return fmt.Errorf("failed to verify customer: %w", err)
	}

	// Check credit limit
	if !customer.HasSufficientCredit(payment.Amount) {
		payment.Status = PaymentStatusFailed
		payment.ErrorMessage = "Insufficient credit limit"
		if err := s.db.WithContext(ctx).Save(payment).Error; err != nil {
			return fmt.Errorf("failed to update payment status: %w", err)
		}
		return fmt.Errorf("insufficient credit limit for customer %s", customerCode)
	}

	// Create payment entry in ERPNext
	paymentEntry := map[string]interface{}{
		"doctype":         "Payment Entry",
		"payment_type":    "Receive",
		"mode_of_payment": "On Account",
		"party_type":      "Customer",
		"party":           customerCode,
		"paid_amount":     payment.Amount,
		"reference_no":    payment.ID,
		"reference_date":  time.Now(),
		"custom_remarks":  fmt.Sprintf("Telegram Bot Order Payment: %s", strings.Join(payment.OrderRefs, ", ")),
	}

	// Create payment entry
	entryRef, err := s.erpnext.CreateDocument(paymentEntry)
	if err != nil {
		payment.Status = PaymentStatusFailed
		payment.ErrorMessage = fmt.Sprintf("Failed to create payment entry: %v", err)
		if err := s.db.WithContext(ctx).Save(payment).Error; err != nil {
			return fmt.Errorf("failed to update payment status: %w", err)
		}
		return fmt.Errorf("failed to create payment entry: %w", err)
	}

	// Update payment status
	payment.Status = PaymentStatusComplete
	payment.Reference = entryRef.Get("name").(string)
	completedAt := time.Now()
	payment.CompletedAt = &completedAt

	if err := s.db.WithContext(ctx).Save(payment).Error; err != nil {
		log.Printf("Warning: Failed to update payment status: %v", err)
	}

	// Submit orders
	for _, orderRef := range payment.OrderRefs {
		if err := s.erpnext.SubmitDocument("Sales Order", orderRef); err != nil {
			log.Printf("Warning: Failed to submit order %s: %v", orderRef, err)
		}
	}

	return nil
}

// HandleMPesaCallback processes M-Pesa payment callbacks
func (s *PaymentService) HandleMPesaCallback(ctx context.Context, callback *MPesaCallback) error {
	stkCallback := callback.Body.StkCallback
	checkoutRequestID := stkCallback.CheckoutRequestID
	resultCode := stkCallback.ResultCode
	resultDesc := stkCallback.ResultDesc

	// Get the payment ID from Redis
	key := fmt.Sprintf("mpesa:checkout:%s", checkoutRequestID)
	paymentID, err := s.redis.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return fmt.Errorf("no payment found for checkout request ID: %s", checkoutRequestID)
		}
		return fmt.Errorf("redis error: %w", err)
	}

	// Get payment record using the paymentID from Redis
	var payment Payment
	if err := s.db.WithContext(ctx).Where("id = ?", paymentID).First(&payment).Error; err != nil {
		return fmt.Errorf("failed to get payment record: %w", err)
	}

	// Get user's warehouse if not already set
	if payment.Warehouse == "" {
		warehouseKey := fmt.Sprintf("user:warehouse:%d", payment.UserID)
		warehouse, err := s.redis.Get(ctx, warehouseKey).Result()
		if err != nil || warehouse == "" {
			warehouse = "Main Warehouse" // Default warehouse if none assigned
		}
		payment.Warehouse = warehouse
		if err := s.db.Save(&payment).Error; err != nil {
			log.Printf("Warning: Failed to update payment warehouse: %v", err)
		}
	}

	// Process based on result code
	if resultCode == 0 {
		// Success - extract payment details
		var amount, mpesaReceiptNumber string

		for _, item := range stkCallback.CallbackMetadata.Item {
			switch item.Name {
			case "Amount":
				if val, ok := item.Value.(float64); ok {
					amount = fmt.Sprintf("%.2f", val)
				}
			case "MpesaReceiptNumber":
				if val, ok := item.Value.(string); ok {
					mpesaReceiptNumber = val
				}
			}
		}

		// Update payment in database
		if err := s.CompletePayment(ctx, paymentID, mpesaReceiptNumber, amount); err != nil {
			return fmt.Errorf("failed to complete payment: %w", err)
		}
	} else {
		// Failed payment
		if err := s.FailPayment(ctx, paymentID, resultDesc); err != nil {
			return fmt.Errorf("failed to mark payment as failed: %w", err)
		}
	}

	// Clean up Redis key
	if err := s.redis.Del(ctx, key).Err(); err != nil {
		log.Printf("Warning: Failed to delete Redis key %s: %v", key, err)
	}

	return nil
}

// GetPaymentStatus returns current payment status
func (s *PaymentService) GetPaymentStatus(ctx context.Context, paymentID string) (*Payment, error) {
	var payment Payment
	if err := s.db.WithContext(ctx).Where("id = ?", paymentID).First(&payment).Error; err != nil {
		return nil, err
	}
	return &payment, nil
}

// CancelPayment cancels a pending payment
func (s *PaymentService) CancelPayment(ctx context.Context, paymentID string) error {
	// Get the payment record
	var payment Payment
	if err := s.db.WithContext(ctx).Where("id = ?", paymentID).First(&payment).Error; err != nil {
		return fmt.Errorf("failed to find payment: %w", err)
	}

	// Only allow cancellation of pending payments
	if payment.Status != "pending" {
		return fmt.Errorf("cannot cancel payment in %s status", payment.Status)
	}

	// Check if payment has been pending for more than 5 minutes
	if time.Since(payment.CreatedAt) > 5*time.Minute {
		// Update payment status to cancelled
		if err := s.db.WithContext(ctx).Model(&payment).Updates(map[string]interface{}{
			"status":       "cancelled",
			"cancelled_at": time.Now(),
		}).Error; err != nil {
			return fmt.Errorf("failed to update payment status: %w", err)
		}

		// Clean up any Redis keys associated with this payment
		if payment.Reference != "" {
			key := fmt.Sprintf("mpesa:checkout:%s", payment.Reference)
			if err := s.redis.Del(ctx, key).Err(); err != nil {
				log.Printf("Warning: Failed to delete Redis key %s: %v", key, err)
			}
		}

		// Clean up any pending orders
		for _, orderRef := range payment.OrderRefs {
			if err := s.erpnext.DeleteDocument("Sales Order", orderRef); err != nil {
				log.Printf("Failed to cleanup order %s: %v", orderRef, err)
			}
		}

		return fmt.Errorf("payment_timeout")
	}

	return fmt.Errorf("payment_pending")
}

// InitiateMPesaPayment starts the M-Pesa payment process for a customer's cart
func (s *PaymentService) InitiateMPesaPayment(ctx context.Context, paymentCtx *types.PaymentContext) error {
	// Get warehouse from the first cart item
	var warehouse string
	for _, item := range paymentCtx.Customer.Cart {
		warehouse = item.Warehouse
		break // Use the warehouse from the first item
	}

	if warehouse == "" {
		return fmt.Errorf("no warehouse found in cart items")
	}

	log.Printf("Using warehouse from cart: %s", warehouse)

	customer := paymentCtx.Customer
	var payment *Payment

	// Check if this is a retry
	if paymentCtx.PaymentID != "" {
		existingPayment, err := s.GetPaymentStatus(ctx, paymentCtx.PaymentID)
		if err != nil {
			return fmt.Errorf("failed to get existing payment: %w", err)
		}

		if existingPayment.Status != PaymentStatusFailed {
			return fmt.Errorf("can only retry failed payments, current status: %s", existingPayment.Status)
		}

		// Verify we have order references in the existing payment
		if len(existingPayment.OrderRefs) == 0 {
			return fmt.Errorf("no order references found in existing payment")
		}

		payment = existingPayment
		payment.Status = PaymentStatusPending
		payment.ErrorMessage = ""

		log.Printf("Retrying payment with order references: %v", payment.OrderRefs)

	} else {
		log.Printf("Initiating new M-Pesa payment for customer: %s with phone: %s",
			customer.CustomerCode, customer.Mobile)

		// Calculate total amount
		var totalAmount float64
		cartItems := make(map[string]*CartItem)

		for productID, item := range customer.Cart {
			totalAmount += item.Price * float64(item.Quantity)
			cartItems[productID] = &CartItem{
				ProductID: productID,
				Quantity:  item.Quantity,
				Price:     item.Price,
				Warehouse: item.Warehouse, // Use the warehouse from each cart item
			}
		}

		if totalAmount <= 0 {
			return fmt.Errorf("invalid cart total amount")
		}

		// Create draft orders with warehouse
		orderRefs, err := s.orderService.CreateDraftOrder(paymentCtx.UserID, cartItems)
		if err != nil {
			return fmt.Errorf("failed to create order: %w", err)
		}

		log.Printf("Created draft orders: %v", orderRefs) // Add logging to see order refs

		// Verify we have order references
		if len(orderRefs) == 0 {
			return fmt.Errorf("no order references returned from CreateDraftOrder")
		}

		// Create new payment record
		payment = &Payment{
			ID:           uuid.New().String(),
			UserID:       paymentCtx.UserID,
			CustomerCode: customer.CustomerCode,
			OrderRefs:    orderRefs, // Make sure orderRefs is being assigned
			Amount:       totalAmount,
			Method:       PaymentMethodMPesa,
			Status:       PaymentStatusPending,
			PhoneNumber:  customer.Mobile,
			Warehouse:    warehouse,
			CreatedAt:    time.Now(),
			ChatID:       paymentCtx.ChatID,
			MessageID:    paymentCtx.MessageID,
		}

		if err := s.db.WithContext(ctx).Create(payment).Error; err != nil {
			// Cleanup orders on payment creation failure
			for _, ref := range orderRefs {
				if err := s.erpnext.DeleteDocument("Sales Order", ref); err != nil {
					log.Printf("Failed to cleanup order %s: %v", ref, err)
				}
			}
			return fmt.Errorf("failed to create payment record: %w", err)
		}
	}

	// Get the first SO reference to use as account reference
	if len(payment.OrderRefs) == 0 {
		return fmt.Errorf("no order references found in payment record")
	}

	log.Printf("Using order reference for payment: %s", payment.OrderRefs[0])

	// Initiate STK Push
	stkResponse, err := s.mpesa.InitiateSTKPush(
		payment.PhoneNumber,
		payment.Amount,
		payment.OrderRefs[0], // Use first SO reference
		"Order Payment",
	)
	if err != nil {
		payment.Status = PaymentStatusFailed
		payment.ErrorMessage = fmt.Sprintf("STK push failed: %v", err)
		if err := s.db.WithContext(ctx).Save(payment).Error; err != nil {
			log.Printf("Failed to update payment status: %v", err)
		}

		return fmt.Errorf("failed to initiate MPesa payment: %w", err)
	}

	// Store the M-Pesa checkout request ID
	payment.Reference = stkResponse.CheckoutRequestID
	if err := s.db.WithContext(ctx).Save(payment).Error; err != nil {
		return fmt.Errorf("failed to update payment with reference: %w", err)
	}

	// Store payment ID in Redis
	key := fmt.Sprintf("mpesa:checkout:%s", stkResponse.CheckoutRequestID)
	if err := s.redis.Set(ctx, key, payment.ID, 15*time.Minute).Err(); err != nil {
		log.Printf("Warning: Failed to store payment reference in Redis: %v", err)
	}

	// Schedule status check
	s.mpesa.ScheduleTransactionStatusCheck(ctx, stkResponse.CheckoutRequestID, payment.ID, s.redis)

	// Update or send new message
	msg := &strings.Builder{}
	msg.WriteString("*" + escapeMarkdownV2("Payment Initiated") + "*\n\n")
	msg.WriteString(escapeMarkdownV2("Please check your phone for the M-Pesa prompt") + "\n")
	msg.WriteString(escapeMarkdownV2("Enter PIN to complete payment"))

	buttons := [][]telebot.InlineButton{
		{{Text: "🔄 Check Status", Data: fmt.Sprintf("payment:status:%s", payment.ID)}},
		{{Text: "❌ Cancel", Data: fmt.Sprintf("payment:cancel:%s", payment.ID)}},
	}

	if payment.MessageID != 0 {
		// Update existing message
		_, err = s.bot.Edit(
			&telebot.Message{ID: payment.MessageID, Chat: &telebot.Chat{ID: payment.ChatID}},
			msg.String(),
			&telebot.ReplyMarkup{InlineKeyboard: buttons},
			telebot.ModeMarkdownV2,
		)
	} else {
		// Send new message
		sentMsg, err := s.bot.Send(
			&telebot.Chat{ID: payment.ChatID},
			msg.String(),
			&telebot.ReplyMarkup{InlineKeyboard: buttons},
			telebot.ModeMarkdownV2,
		)
		if err == nil {
			payment.MessageID = sentMsg.ID
			payment.ChatID = sentMsg.Chat.ID
			s.db.WithContext(ctx).Save(payment)
		}
	}

	return err
}

// Global payment service instance for access from handlers
var globalPaymentService *PaymentService

// GetPaymentService returns the global payment service instance
func GetPaymentService() *PaymentService {
	return globalPaymentService
}

// SetGlobalPaymentService sets the global payment service instance
func SetGlobalPaymentService(service *PaymentService) {
	globalPaymentService = service
}

// Helper function to escape special characters for MarkdownV2
func escapeMarkdownV2(text string) string {
	specialChars := []string{"_", "*", "[", "]", "(", ")", "~", "`", ">", "#", "+", "-", "=", "|", "{", "}", ".", "!", ","}
	escaped := text
	for _, char := range specialChars {
		escaped = strings.ReplaceAll(escaped, char, "\\"+char)
	}
	return escaped
}

// CompletePayment marks a payment as complete with the provided receipt number
func (s *PaymentService) CompletePayment(ctx context.Context, paymentID string, mpesaReceiptNumber string, amount string) error {
	// Get the payment record
	var payment Payment
	if err := s.db.WithContext(ctx).Where("id = ?", paymentID).First(&payment).Error; err != nil {
		return fmt.Errorf("failed to get payment record: %w", err)
	}

	// Update payment status
	payment.Status = PaymentStatusComplete
	payment.Reference = mpesaReceiptNumber
	completedAt := time.Now()
	payment.CompletedAt = &completedAt
	warehouse := payment.Warehouse

	var invoiceRefs []string

	// 1. Submit Sales Orders and Create Sales Invoices
	for _, orderRef := range payment.OrderRefs {
		// First, get the Sales Order details
		orderDoc, err := s.erpnext.GetDocument("Sales Order", orderRef)
		if err != nil {
			log.Printf("Warning: Failed to fetch order %s: %v", orderRef, err)
			continue
		}

		// Debug: Print the entire Sales Order document
		log.Printf("Sales Order Document: %+v", orderDoc.Message)

		// Submit the Sales Order
		if err := s.erpnext.SubmitDocumentMethod("Sales Order", orderRef); err != nil {
			log.Printf("Warning: Failed to submit order %s: %v", orderRef, err)
			continue
		}

		// Parse the items field correctly
		items, ok := orderDoc.Message["items"]
		if !ok {
			log.Printf("Warning: 'items' field not found in Sales Order %s", orderRef)
			continue
		}

		// Convert items to a slice of maps
		var orderItems []map[string]interface{}
		switch v := items.(type) {
		case []interface{}:
			for _, item := range v {
				if itemMap, ok := item.(map[string]interface{}); ok {
					orderItems = append(orderItems, itemMap)
				}
			}
		default:
			log.Printf("Warning: Invalid 'items' format in Sales Order %s: expected []interface{}, got %T", orderRef, v)
			continue
		}

		if len(orderItems) == 0 {
			log.Printf("Warning: No valid items found in Sales Order %s", orderRef)
			continue
		}

		// Prepare items for Sales Invoice
		invoiceItems := make([]map[string]interface{}, 0)
		for _, item := range orderItems {
			invoiceItem := map[string]interface{}{
				"item_code":           item["item_code"],
				"qty":                 item["qty"],
				"rate":                item["rate"],
				"warehouse":           warehouse,
				"sales_order":         orderRef,
				"against_sales_order": orderRef,
			}
			invoiceItems = append(invoiceItems, invoiceItem)
		}

		// Create Sales Invoice
		salesInvoice := map[string]interface{}{
			"doctype":          "Sales Invoice",
			"customer":         "WALK-IN",
			"is_pos":           0,
			"update_stock":     1, // This ensures stock is updated
			"set_posting_time": 1,
			"posting_date":     time.Now().Format("2006-01-02"),
			"company":          "Tuiyo Farmers Cooperative Society",
			"sales_order":      orderRef,
			"items":            invoiceItems,
			"warehouse":        warehouse,
		}

		invoiceResp, err := s.erpnext.CreateDocument(salesInvoice)
		if err != nil {
			log.Printf("Warning: Failed to create sales invoice for order %s: %v", orderRef, err)
			continue
		}

		invoiceRef := invoiceResp.Get("name").(string)
		invoiceRefs = append(invoiceRefs, invoiceRef)

		// Submit the Sales Invoice to finalize stock deduction
		if err := s.erpnext.SubmitDocumentMethod("Sales Invoice", invoiceRef); err != nil {
			log.Printf("Warning: Failed to submit sales invoice %s: %v", invoiceRef, err)
			continue
		}
	}

	// 2. Create Payment Entry with references to all invoices
	// Get the accounts from configuration or settings
	paidFrom := "Debtors - TDPL" // Replace with actual account
	paidTo := "Cash - TDPL"      // Replace with actual account

	// Create references for all invoices
	references := make([]map[string]interface{}, 0, len(invoiceRefs))
	totalAmount := payment.Amount
	remainingAmount := totalAmount

	// Distribute the payment amount across invoices
	for i, invoiceRef := range invoiceRefs {
		var allocatedAmount float64
		if i == len(invoiceRefs)-1 {
			// Allocate remaining amount to last invoice to handle rounding
			allocatedAmount = remainingAmount
		} else {
			// Distribute amount evenly
			allocatedAmount = totalAmount / float64(len(invoiceRefs))
			remainingAmount -= allocatedAmount
		}

		reference := map[string]interface{}{
			"reference_doctype":  "Sales Invoice",
			"reference_name":     invoiceRef,
			"allocated_amount":   allocatedAmount,
			"due_date":           time.Now().Format("2006-01-02"),
			"total_amount":       allocatedAmount,
			"outstanding_amount": allocatedAmount,
		}
		references = append(references, reference)
	}

	// Create Payment Entry
	paymentEntry := map[string]interface{}{
		"doctype":         "Payment Entry",
		"payment_type":    "Receive",
		"mode_of_payment": "M-Pesa",
		"party_type":      "Customer",
		"party":           "WALK-IN",
		"paid_amount":     payment.Amount,
		"received_amount": payment.Amount,
		"reference_no":    mpesaReceiptNumber,
		"reference_date":  time.Now().Format("2006-01-02"),
		"posting_date":    time.Now().Format("2006-01-02"),
		"company":         "Tuiyo Farmers Cooperative Society",
		"paid_from":       paidFrom,
		"paid_to":         paidTo,
		"source":          "Mobile Payment",
		"references":      references,
		"remarks":         fmt.Sprintf("M-Pesa Payment %s for orders: %s", mpesaReceiptNumber, strings.Join(payment.OrderRefs, ", ")),
	}

	// Create payment entry in ERPNext
	entryResp, err := s.erpnext.CreateDocument(paymentEntry)
	if err != nil {
		log.Printf("Warning: Failed to create payment entry: %v", err)
		return fmt.Errorf("failed to create payment entry: %w", err)
	}

	// Get the payment entry reference
	paymentEntryRef := entryResp.Get("name").(string)
	payment.Reference = paymentEntryRef

	// Submit the Payment Entry
	if err := s.erpnext.SubmitDocumentMethod("Payment Entry", paymentEntryRef); err != nil {
		log.Printf("Warning: Failed to submit payment entry %s: %v", paymentEntryRef, err)
		return fmt.Errorf("failed to submit payment entry: %w", err)
	}

	// Update payment record in database
	if err := s.db.WithContext(ctx).Save(&payment).Error; err != nil {
		log.Printf("Warning: Failed to update payment reference: %v", err)
		return fmt.Errorf("failed to update payment record: %w", err)
	}

	// Notify user about successful payment
	if err := s.NotifyPaymentSuccess(ctx, paymentID); err != nil {
		log.Printf("Warning: Failed to notify user about successful payment: %v", err)
	}

	return nil
}

// FailPayment marks a payment as failed with the provided error message
func (s *PaymentService) FailPayment(ctx context.Context, paymentID string, errorMessage string) error {
	// Get the payment record
	var payment Payment
	if err := s.db.WithContext(ctx).Where("id = ?", paymentID).First(&payment).Error; err != nil {
		return fmt.Errorf("failed to get payment record: %w", err)
	}

	// Update payment status
	payment.Status = PaymentStatusFailed
	payment.ErrorMessage = errorMessage

	// Save the updated payment
	if err := s.db.WithContext(ctx).Save(&payment).Error; err != nil {
		return fmt.Errorf("failed to update payment status: %w", err)
	}

	// Notify user about failed payment
	if err := s.NotifyPaymentFailure(ctx, paymentID, errorMessage); err != nil {
		log.Printf("Warning: Failed to notify user about failed payment: %v", err)
	}

	return nil
}

// NotifyPaymentSuccess updates the same message with success status
func (s *PaymentService) NotifyPaymentSuccess(ctx context.Context, paymentID string) error {
	var payment Payment
	if err := s.db.WithContext(ctx).Where("id = ?", paymentID).First(&payment).Error; err != nil {
		return fmt.Errorf("failed to get payment record: %w", err)
	}

	msg := &strings.Builder{}
	msg.WriteString("✅ *" + escapeMarkdownV2("Payment Successful") + "*\n\n")
	msg.WriteString(escapeMarkdownV2(fmt.Sprintf("Amount: %.2f", payment.Amount)) + "\n")
	msg.WriteString(escapeMarkdownV2(fmt.Sprintf("Reference: %s", payment.Reference)) + "\n")
	msg.WriteString(escapeMarkdownV2("Thank you for your payment!"))

	buttons := [][]telebot.InlineButton{
		{{Text: "🏪 Continue Shopping", Data: "shop:browse"}},
		{{Text: "📦 View Orders", Data: "orders:list"}},
	}

	// Create a proper message object
	messageToEdit := &telebot.Message{
		ID: payment.MessageID,
		Chat: &telebot.Chat{
			ID: payment.ChatID,
		},
	}

	_, err := s.bot.Edit(
		messageToEdit,
		msg.String(),
		&telebot.ReplyMarkup{InlineKeyboard: buttons},
		telebot.ModeMarkdownV2,
	)

	if err != nil {
		log.Printf("Error updating payment success message: %v", err)
		// Try sending a new message if editing fails
		_, err = s.bot.Send(
			&telebot.Chat{ID: payment.ChatID},
			msg.String(),
			telebot.ModeMarkdownV2,
		)
		if err != nil {
			return fmt.Errorf("failed to send payment success message: %w", err)
		}
	}

	return nil
}

// NotifyPaymentFailure updates the same message with failure status
func (s *PaymentService) NotifyPaymentFailure(ctx context.Context, paymentID string, errorMessage string) error {
	var payment Payment
	if err := s.db.WithContext(ctx).Where("id = ?", paymentID).First(&payment).Error; err != nil {
		return fmt.Errorf("failed to get payment record: %w", err)
	}

	msg := &strings.Builder{}
	msg.WriteString("*" + escapeMarkdownV2("Payment Failed") + "*\n\n")
	msg.WriteString(escapeMarkdownV2(fmt.Sprintf("Amount: %.2f", payment.Amount)) + "\n")
	msg.WriteString(escapeMarkdownV2(fmt.Sprintf("Error: %s", errorMessage)) + "\n")
	msg.WriteString(escapeMarkdownV2("Please try again or choose a different payment method."))

	buttons := [][]telebot.InlineButton{
		{{Text: "🔄 Try Again", Data: fmt.Sprintf("payment:retry:%s", payment.ID)}},
		{{Text: "🔙 Back to Cart", Data: "checkout:back"}},
	}

	_, err := s.bot.Edit(
		&telebot.Message{
			ID: payment.MessageID,
			Chat: &telebot.Chat{
				ID: payment.ChatID,
			},
		},
		msg.String(),
		&telebot.ReplyMarkup{InlineKeyboard: buttons},
		telebot.ModeMarkdownV2,
	)

	if err != nil {
		log.Printf("Error updating payment failure message: %v", err)
		return fmt.Errorf("failed to update payment failure message: %w", err)
	}

	return nil
}

// Helper function to create invoice references for payment entry
// func createInvoiceReferences(invoiceRefs []string, totalAmount float64) []map[string]interface{} {
// 	var references []map[string]interface{}
// 	numInvoices := len(invoiceRefs)
// 	if numInvoices == 0 {
// 		return references
// 	}

// 	// Distribute amount equally among invoices
// 	amountPerInvoice := totalAmount / float64(numInvoices)

// 	for _, invoiceRef := range invoiceRefs {
// 		references = append(references, map[string]interface{}{
// 			"reference_doctype": "Sales Invoice",
// 			"reference_name":    invoiceRef,
// 			"allocated_amount":  amountPerInvoice,
// 		})
// 	}

// 	return references
// }

// GetPayment retrieves a payment record by ID
func (s *PaymentService) GetPayment(ctx context.Context, paymentID string) (*Payment, error) {
	var payment Payment
	if err := s.db.WithContext(ctx).Where("id = ?", paymentID).First(&payment).Error; err != nil {
		return nil, fmt.Errorf("failed to get payment: %w", err)
	}
	return &payment, nil
}

func (s *PaymentService) UpdatePaymentWarehouse(ctx context.Context, paymentID string, warehouse string) error {
	result := s.db.Model(&Payment{}).
		Where("id = ?", paymentID).
		Update("warehouse", warehouse)

	if result.Error != nil {
		return fmt.Errorf("failed to update payment warehouse: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("payment not found: %s", paymentID)
	}

	return nil
}
