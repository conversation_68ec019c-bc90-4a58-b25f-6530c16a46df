package models

import (
	"time"
	
	"gorm.io/gorm"
)

type User struct {
	ID         uint           `gorm:"primaryKey"`
	TelegramID int64         `gorm:"uniqueIndex;not null"`
	Mobile     string         `gorm:"uniqueIndex;size:20"`
	ERPNextID  string         `gorm:"column:erpnext_id;size:40"`
	IsActive   bool           `gorm:"default:true"`
	CreatedAt  time.Time
	UpdatedAt  time.Time
	LastLogin  time.Time
	DeletedAt  gorm.DeletedAt `gorm:"index"`
}
