package models

// ERPNextBinResponse represents the bin/stock level response
type ERPNextBinResponse struct {
    Data []ERPNextBin `json:"data"`
}

type ERPNextBin struct {
    Name                string  `json:"name"`
    ItemCode            string  `json:"item_code"`
    Warehouse           string  `json:"warehouse"`
    ActualQty           float64 `json:"actual_qty"`
    ProjectedQty        float64 `json:"projected_qty"`
    StockUOM            string  `json:"stock_uom"`
    ValuationRate       float64 `json:"valuation_rate"`
    StockValue          float64 `json:"stock_value"`
}

// ERPNextItemResponse represents the item response
type ERPNextItemResponse struct {
    Data []ERPNextItem `json:"data"`
}

type ERPNextItem struct {
    Name            string  `json:"name"`
    ItemCode        string  `json:"item_code"`
    ItemName        string  `json:"item_name"`
    ItemGroup       string  `json:"item_group"`
    Description     string  `json:"description"`
    StockUOM        string  `json:"stock_uom"`
    IsStockItem     int     `json:"is_stock_item"`
    IsSalesItem     int     `json:"is_sales_item"`
    StandardRate    float64 `json:"standard_rate"`
    ValuationRate   float64 `json:"valuation_rate"`
    Disabled        int     `json:"disabled"`
}

// ERPNextItemGroupResponse represents the item group response
type ERPNextItemGroupResponse struct {
    Data []ERPNextItemGroup `json:"data"`
}

type ERPNextItemGroup struct {
    Name            string `json:"name"`
    ItemGroupName   string `json:"item_group_name"`
    ParentItemGroup string `json:"parent_item_group"`
    IsGroup         int    `json:"is_group"`
}

// Transform functions to convert ERPNext responses to internal models
func (item *ERPNextItem) ToProduct(bin *ERPNextBin) Product {
    return Product{
        ID:         item.ItemCode,
        CategoryID: item.ItemGroup,
        Name:       item.ItemName,
        Price:      item.StandardRate,
        StockQty:   int(bin.ActualQty),
    }
}

func (group *ERPNextItemGroup) ToCategory() Category {
    return Category{
        ID:          group.Name,
        Name:        group.ItemGroupName,
        ParentID:    group.ParentItemGroup,
        IsGroup:     group.IsGroup == 1,
    }
}