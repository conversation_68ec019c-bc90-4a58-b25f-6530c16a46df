package models

// Category represents a product category
type Category struct {
	ID          string
	Name        string
	Description string
	ParentID    string
	IsGroup     bool
}

// Product represents a product from ERPNext
type Product struct {
	ID          string
	CategoryID  string
	Name        string
	Price       float64
	StockQty    int
}

// Order represents a sales order
type Order struct {
	ID       string
	UserID   int64
	Items    []OrderItem
	Total    float64
	Status   string
}

// OrderItem represents an item in an order
type OrderItem struct {
	ProductID string
	Quantity  int
	Price     float64
	Subtotal  float64
}
